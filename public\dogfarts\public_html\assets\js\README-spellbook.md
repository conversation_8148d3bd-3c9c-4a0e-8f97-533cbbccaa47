# Magical Spellbook Generator

A JavaScript-based spellbook generator that displays random magical advice in a stylized popup with a wand cursor animation on hover.

## Features

- **Random Advice Generator**: Displays a random piece of magical advice from a predefined array
- **Stylized Popup**: Shows the advice in a parchment-like, magically styled popup
- **Wand Cursor Animation**: Custom wand cursor appears on hover with sparkle effects
- **Responsive Design**: Works on all screen sizes
- **Accessibility**: Keyboard navigation support (ESC to close popup)
- **Magical Effects**: Includes sparkle animations and hover effects

## How to Use

### 1. Include Required Files

Add these files to your project:

```html
<!-- In the <head> section -->
<link rel="stylesheet" href="assets/css/spellbook-generator.css">

<!-- Before the closing </body> tag -->
<script src="assets/js/spellbook-generator.js"></script>
```

### 2. Add the Button to Your HTML

Add this button where you want the spellbook generator to appear:

```html
<button id="spellbook-generator" class="spellbook-button">
  Open the Ancient Spellbook
</button>
```

**Important**: The button must have the ID `spellbook-generator` for the JavaScript to work.

### 3. Add Wand Cursor Support (Optional)

To enable the wand cursor across your entire site, add this script:

```html
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Add wand-active class to body when mouse enters the page
    document.addEventListener('mouseenter', function() {
      document.body.classList.add('wand-active');
    });
    
    // Remove wand-active class when mouse leaves the page
    document.addEventListener('mouseleave', function() {
      document.body.classList.remove('wand-active');
    });
  });
</script>
```

## Customization

### Modifying the Advice Array

To change the magical advice displayed, edit the `magicalAdvice` array in `spellbook-generator.js`:

```javascript
const magicalAdvice = [
    "Your first piece of advice",
    "Your second piece of advice",
    // Add more advice here
];
```

### Styling the Button

You can customize the button appearance by modifying the `.spellbook-button` class in the CSS file.

### Changing the Popup Style

The popup styling can be modified in the CSS file:

- `.spell-popup-overlay`: The dark background overlay
- `.spell-popup-content`: The main popup container
- `.spell-popup-header`: The popup header
- `.spell-popup-body`: The popup body containing the advice
- `.spell-popup-footer`: The popup footer

### Customizing the Wand Cursor

To change the wand cursor, modify the `background-image` property of the `.wand-cursor` class in the CSS file. The current implementation uses an SVG data URI, but you can replace it with a path to an image file:

```css
.wand-cursor {
  background-image: url('path/to/your/wand-image.png');
}
```

## Browser Compatibility

This component is compatible with:
- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Opera (latest)

## Dependencies

- Google Fonts: Playfair Display and Raleway (for the default styling)

## License

This spellbook generator is free to use for both personal and commercial projects.

## Credits

- SVG patterns and borders created with custom SVG code
- Wand icon created with custom SVG
