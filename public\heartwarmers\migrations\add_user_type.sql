-- Migration to add user_type field to existing users table
-- Run this if you have an existing database without the user_type field

USE heartwarmers;

-- Add user_type column if it doesn't exist
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS user_type ENUM('help_seeker', 'business_organization', 'volunteer_mutual_aid') DEFAULT 'help_seeker' 
AFTER slug;

-- Update existing users to have the default user_type if they don't have one
UPDATE users 
SET user_type = 'help_seeker' 
WHERE user_type IS NULL;
