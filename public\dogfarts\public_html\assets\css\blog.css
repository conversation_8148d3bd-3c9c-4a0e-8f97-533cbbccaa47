/* Blog-specific styles */

/* Common blog styles */
.blog-main h1,
.blog-post h1,
.blog-category h1 {
    color: #4a2c82;
    margin-bottom: 1em;
}

.error-message,
.not-found,
.no-posts {
    background-color: #f8f8f8;
    border-radius: 5px;
    padding: 20px;
    margin-bottom: 30px;
    text-align: center;
}

.error-message {
    border-left: 4px solid #e53935;
}

.not-found h1,
.no-posts h1 {
    color: #333;
}

/* Blog post list */
.posts-container {
    display: grid;
    grid-gap: 30px;
    margin-bottom: 30px;
}

.post {
    background-color: white;
    border-radius: 5px;
    padding: 25px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.post:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.post-title {
    margin-bottom: 15px;
}

.post-title a {
    color: #4a2c82;
    text-decoration: none;
}

.post-title a:hover {
    color: #3a2065;
    text-decoration: underline;
}

.post-meta {
    color: #666;
    font-size: 0.9em;
    margin-bottom: 20px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
}

.post-date {
    margin-right: 15px;
}

.post-updated {
    margin-right: 15px;
    font-style: italic;
}

.post-author {
    margin-right: 15px;
}

.post-categories {
    margin-top: 10px;
    width: 100%;
}

.post-excerpt {
    margin-bottom: 20px;
    line-height: 1.6;
}

.read-more {
    display: inline-block;
    background-color: #4a2c82;
    color: white;
    padding: 8px 15px;
    border-radius: 3px;
    font-size: 0.9em;
    transition: background-color 0.2s ease;
}

.read-more:hover {
    background-color: #3a2065;
    text-decoration: none;
}

/* Single blog post */
.blog-post article,
.blog-category article {
    background-color: white;
    border-radius: 5px;
    padding: 30px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.blog-post header,
.blog-category header {
    background-color: transparent;
    padding: 0;
    margin-bottom: 25px;
    border-bottom: 1px solid #eee;
    padding-bottom: 15px;
}

.post-content {
    line-height: 1.8;
}

.post-content p {
    margin-bottom: 1.5em;
}

.post-content img {
    max-width: 100%;
    height: auto;
    margin: 20px 0;
    border-radius: 5px;
}

.post-content ul,
.post-content ol {
    margin-bottom: 1.5em;
    padding-left: 20px;
}

.post-content li {
    margin-bottom: 0.5em;
}

.post-footer {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.post-navigation,
.category-navigation {
    margin-top: 30px;
}

.back-to-blog {
    display: inline-block;
    color: #4a2c82;
    font-weight: bold;
    text-decoration: none;
}

.back-to-blog:hover {
    text-decoration: underline;
}

/* Category styles */
.category-description {
    background-color: #f8f8f8;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 25px;
    font-style: italic;
}

/* Blog categories list */
.blog-categories {
    margin-bottom: 30px;
    background-color: white;
    border-radius: 5px;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.blog-categories h3 {
    margin-bottom: 15px;
    color: #4a2c82;
    border-bottom: 2px solid #eee;
    padding-bottom: 10px;
}

.blog-categories ul {
    list-style: none;
    display: flex;
    flex-wrap: wrap;
}

.blog-categories li {
    margin-right: 10px;
    margin-bottom: 10px;
}

.blog-categories a {
    display: block;
    background-color: #f5f5f5;
    padding: 8px 15px;
    border-radius: 3px;
    color: #333;
    transition: all 0.2s ease;
}

.blog-categories a:hover,
.blog-categories a.active {
    background-color: #4a2c82;
    color: white;
    text-decoration: none;
}

/* Search styles */
.search-form {
    margin-bottom: 30px;
}

.search-input {
    display: flex;
    max-width: 600px;
}

.search-input input[type="text"] {
    flex: 1;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 3px 0 0 3px;
    font-size: 1em;
}

.search-input button {
    padding: 10px 15px;
    background-color: #4a2c82;
    color: white;
    border: none;
    border-radius: 0 3px 3px 0;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.search-input button:hover {
    background-color: #3a2065;
}

.search-results-count {
    margin-bottom: 20px;
    font-style: italic;
    color: #666;
}

.no-results {
    background-color: #f8f8f8;
    padding: 20px;
    border-radius: 5px;
    margin-bottom: 30px;
    text-align: center;
}

.search-navigation {
    margin-top: 30px;
}

/* Blog sidebar widgets */
.widget.recent-posts ul {
    list-style: none;
    padding: 0;
}

.widget.recent-posts li {
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.widget.recent-posts li:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.widget.recent-posts a {
    display: block;
    color: #4a2c82;
    margin-bottom: 5px;
}

.widget.recent-posts .post-date {
    display: block;
    font-size: 0.8em;
    color: #666;
}

.widget.blog-search .search-input {
    margin-top: 10px;
}

.widget.school-info p {
    margin-bottom: 15px;
}

/* Responsive styles */
@media (max-width: 768px) {
    .post-meta {
        flex-direction: column;
        align-items: flex-start;
    }

    .post-date,
    .post-author,
    .post-updated {
        margin-right: 0;
        margin-bottom: 5px;
    }

    .search-input {
        flex-direction: column;
    }

    .search-input input[type="text"] {
        border-radius: 3px;
        margin-bottom: 10px;
    }

    .search-input button {
        border-radius: 3px;
    }
}
