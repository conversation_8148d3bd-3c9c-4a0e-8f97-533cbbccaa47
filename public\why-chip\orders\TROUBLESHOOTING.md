# Order Form Troubleshooting Guide

## Issues Fixed

### 1. Configuration Path Issue
**Problem:** Config file was not being loaded correctly
**Solution:** Fixed the include path from `'orders/config.php'` to `'orders/orders/config.php'`

### 2. Added Comprehensive Error Logging
**Enhancement:** Added detailed error logging throughout the application to help identify specific issues:
- Order processing steps
- Google Sheets API calls
- Email sending attempts
- CSV file operations
- File permission checks

## Common Issues and Solutions

### Localhost Issues

#### SMTP Configuration Errors
**Symptoms:**
- `Warning: mail(): Failed to connect to mailserver at "localhost" port 25`
- Emails not being sent

**Solutions:**
1. **Configure SMTP in php.ini:**
   ```ini
   [mail function]
   SMTP = smtp.gmail.com
   smtp_port = 587
   sendmail_from = <EMAIL>
   ```

2. **Use MailHog for local testing:**
   - Download and run MailHog
   - Configure php.ini to use MailHog:
     ```ini
     SMTP = localhost
     smtp_port = 1025
     ```

3. **Use Mailtrap for testing:**
   - Sign up for Mailtrap
   - Configure SMTP settings in php.ini

### Web Host Issues

#### Email Delivery Problems
**Symptoms:**
- Form submits successfully but no emails received
- CSV saves but emails fail

**Potential Causes & Solutions:**

1. **SPF/DKIM Records:**
   - Ensure your domain has proper SPF records
   - Set up DKIM authentication
   - Contact your hosting provider for assistance

2. **From Email Domain:**
   - Use an email address from your domain
   - Avoid using generic domains (gmail.com, yahoo.com) in FROM header

3. **Server Mail Configuration:**
   - Check if your hosting provider blocks outgoing mail
   - Verify mail() function is enabled
   - Check server mail logs

4. **Email Reputation:**
   - New domains may have delivery issues
   - Consider using a transactional email service (SendGrid, Mailgun)

#### File Permission Issues
**Symptoms:**
- CSV file not being created or updated
- "Directory not writable" errors

**Solutions:**
1. **Check directory permissions:**
   ```bash
   chmod 755 /path/to/orders/directory
   chmod 644 /path/to/orders.csv
   ```

2. **Ensure proper ownership:**
   ```bash
   chown www-data:www-data /path/to/orders/
   ```

### Google Sheets Issues

#### API Connection Problems
**Symptoms:**
- Orders save to CSV but not to Google Sheets
- Google Sheets API errors in logs

**Solutions:**
1. **Verify Google Apps Script URL:**
   - Ensure the deployment URL is correct
   - Check that the script is deployed as a web app
   - Verify public access permissions

2. **Check Token:**
   - Ensure GOOGLE_APP_TOKEN matches the script
   - Verify token is not expired

3. **Test API Directly:**
   - Use the debug script to test Google Sheets connectivity
   - Check response messages for specific errors

## Debugging Tools

### 1. Debug Test Script
Run `/orders/debug_test.php` to check:
- Configuration loading
- File permissions
- Directory structure
- Google Sheets connectivity
- PHP configuration

### 2. Email Test Script
Run `/orders/email_test.php` to test:
- Basic email functionality
- HTML email formatting
- Different email types (customer/admin)

### 3. Error Logs
Check server error logs for detailed information:
- **cPanel:** `/home/<USER>/public_html/error_logs/`
- **Apache:** `/var/log/apache2/error.log`
- **XAMPP:** `/xampp/apache/logs/error.log`

## Step-by-Step Troubleshooting

### For Localhost Issues:
1. Run `debug_test.php` to identify issues
2. Configure SMTP or use MailHog for email testing
3. Run `email_test.php` to verify email functionality
4. Test the order form with a sample order

### For Web Host Issues:
1. Upload all files to your web server
2. Run `debug_test.php` to check configuration
3. Check file permissions (755 for directories, 644 for files)
4. Run `email_test.php` to test email delivery
5. Check server error logs for specific issues
6. Contact hosting provider if mail() function issues persist

### For Google Sheets Issues:
1. Verify Google Apps Script deployment
2. Test API connectivity with `debug_test.php`
3. Check Google Apps Script logs for errors
4. Ensure proper permissions on the Google Sheet

## Contact Information

If you continue to experience issues after following this guide:
1. Check the error logs for specific error messages
2. Run both debug scripts and note the results
3. Document the exact symptoms and error messages
4. Consider using alternative email services for better reliability

## Alternative Solutions

### Email Alternatives:
- **SendGrid:** Reliable transactional email service
- **Mailgun:** Developer-friendly email API
- **Amazon SES:** Cost-effective email service

### Hosting Considerations:
- Ensure your hosting provider supports PHP mail() function
- Consider VPS or dedicated hosting for better email deliverability
- Use hosting providers with good email reputation
