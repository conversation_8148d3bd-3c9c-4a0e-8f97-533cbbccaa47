// Menu toggle functionality
document.addEventListener('DOMContentLoaded', function() {
    const menuToggle = document.getElementById('menu-toggle');
    const mainMenu = document.getElementById('main-menu');
    
    if (menuToggle && mainMenu) {
        menuToggle.addEventListener('click', function() {
            const expanded = this.getAttribute('aria-expanded') === 'true';
            this.setAttribute('aria-expanded', !expanded);
            mainMenu.setAttribute('aria-expanded', !expanded);
        });
    }
    
    // Initialize ARIA states
    if (mainMenu) {
        mainMenu.setAttribute('aria-expanded', 'false');
    }
});