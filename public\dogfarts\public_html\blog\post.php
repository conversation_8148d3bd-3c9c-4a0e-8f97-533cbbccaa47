<?php
/**
 * Single Blog Post Display
 *
 * This script fetches and displays a single blog post from the database
 * based on the ID provided in the URL.
 */

// Include necessary files
require_once '../config/config.php';
require_once '../config/db.php';
require_once '../lib/functions.php';
include_once '../includes/header.php';
include_once '../includes/nav.php';

// Get post ID from URL
$post_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

// Initialize variables
$post = null;
$categories = [];
$error = '';

// Validate post ID
if ($post_id <= 0) {
    $error = 'Invalid post ID';
} else {
    try {
        // Get database connection
        $db = getDbConnection();

        // Fetch the post with prepared statement
        $stmt = $db->prepare("
            SELECT
                p.id,
                p.title,
                p.content,
                p.date_published,
                p.date_updated,
                p.author_id,
                a.name as author_name
            FROM
                posts p
            LEFT JOIN
                authors a ON p.author_id = a.id
            WHERE
                p.id = :post_id
                AND p.status = 'published'
        ");

        $stmt->bindParam(':post_id', $post_id, PDO::PARAM_INT);
        $stmt->execute();
        $post = $stmt->fetch();

        // If post exists, fetch its categories
        if ($post) {
            $stmt = $db->prepare("
                SELECT
                    c.id,
                    c.name,
                    c.slug
                FROM
                    categories c
                JOIN
                    post_categories pc ON c.id = pc.category_id
                WHERE
                    pc.post_id = :post_id
                ORDER BY
                    c.name ASC
            ");

            $stmt->bindParam(':post_id', $post_id, PDO::PARAM_INT);
            $stmt->execute();
            $categories = $stmt->fetchAll();
        }
    } catch (PDOException $e) {
        // Log the error (in a production environment)
        error_log('Database query error: ' . $e->getMessage());

        // Set error message
        $error = 'Sorry, we could not retrieve the requested blog post.';
    }
}
?>

<main class="blog-post">
    <?php if ($error): ?>
        <div class="error-message">
            <?php echo htmlspecialchars($error); ?>
            <p><a href="index.php">Return to blog listing</a></p>
        </div>
    <?php elseif (!$post): ?>
        <div class="not-found">
            <h1>Post Not Found</h1>
            <p>The requested blog post could not be found.</p>
            <p><a href="index.php">Return to blog listing</a></p>
        </div>
    <?php else: ?>
        <article>
            <header>
                <h1><?php echo htmlspecialchars($post['title']); ?></h1>
                <div class="post-meta">
                    <span class="post-date">
                        Posted on <?php echo formatDate($post['date_published']); ?>
                    </span>

                    <?php if (!empty($post['date_updated']) && $post['date_updated'] != $post['date_published']): ?>
                        <span class="post-updated">
                            (Updated: <?php echo formatDate($post['date_updated']); ?>)
                        </span>
                    <?php endif; ?>

                    <?php if (!empty($post['author_name'])): ?>
                        <span class="post-author">
                            by <?php echo htmlspecialchars($post['author_name']); ?>
                        </span>
                    <?php endif; ?>

                    <?php if (!empty($categories)): ?>
                        <div class="post-categories">
                            Categories:
                            <?php
                            $categoryLinks = [];
                            foreach ($categories as $category) {
                                $categoryLinks[] = '<a href="category.php?slug=' .
                                    htmlspecialchars($category['slug']) . '">' .
                                    htmlspecialchars($category['name']) . '</a>';
                            }
                            echo implode(', ', $categoryLinks);
                            ?>
                        </div>
                    <?php endif; ?>
                </div>
            </header>

            <div class="post-content">
                <?php echo $post['content']; ?>
            </div>

            <footer class="post-footer">
                <div class="post-navigation">
                    <a href="index.php" class="back-to-blog">← Back to Blog</a>
                </div>

                <!-- Social sharing buttons could be added here -->
            </footer>
        </article>
    <?php endif; ?>
</main>

<?php
include 'sidebar.php';
include '../includes/footer.php';
?>
