<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>A. A. Chips' Linktree</title>
  <meta name="description" content="A. A. Chips' Linktree - Switch between simple and expanded views">
  <link rel="shortcut icon" href="favicon.ico" type="image/x-icon">
  <!-- Font Awesome for icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
  <!-- Chatway -->
  <script id="chatway" async="true" src="https://cdn.chatway.app/widget.js?id=QGxjxq7wNQi8"></script>
  <!-- Clarity -->
  <script type="text/javascript">
    (function(c,l,a,r,i,t,y){
      c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
      t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
      y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
      })(window, document, "clarity", "script", "l6wg07j3fq");
  </script> 
  <style>
    /* General Reset */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    /* Custom Fonts */
    @font-face {
      font-family: "Salsa";
      font-weight: 500;
      font-style: italic;
      font-display: swap;
      src: url(https://aachips.co/wp-content/uploads/Salsa-Regular.ttf) format("truetype");
    }

    @font-face {
      font-family: "Virgil";
      src: url('./assets/fonts/Virgil.woff2');
      font-display: swap;
    }

    /* Variables for Light Mode */
    :root {
      --green: #149954;
      --red: #E4312b;
      --black: #000000;
      --white: #fff;
      --background: var(--white);
      --text: var(--black);
      --button-bg: var(--green);
      --button-hover: #107a43;
      --socials-bg: #f2f2f2;
    }

    /* Variables for Dark Mode */
    [data-theme="dark"] {
      --background: var(--black);
      --text: var(--white);
      --button-bg: var(--red);
      --button-hover: #c42a25;
      --socials-bg: #3b3b3b;
    }

    body {
      font-family: "Virgil", sans-serif;
      background: url(./assets/chips/Chips-Pile.png) no-repeat fixed center/cover;
      color: var(--text);
      line-height: 1.6;
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      padding: 20px;
      transition: background-color 0.3s ease, color 0.3s ease;
    }

    .container {
      max-width: 800px;
      width: 100%;
      text-align: center;
      background-color: var(--background);
      padding: 20px;
      border-radius: 10px;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      transition: background-color 0.3s ease, color 0.3s ease;
    }

    /* Theme Toggle */
    .theme-toggle {
      position: absolute;
      top: 20px;
      right: 20px;
    }

    .toggle-label {
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .toggle-label i {
      font-size: 1.2rem;
      color: var(--text);
    }

    #toggle-theme {
      appearance: none;
      width: 40px;
      height: 20px;
      background: var(--button-bg);
      border-radius: 10px;
      cursor: pointer;
      position: relative;
    }

    #toggle-theme:checked::before {
      content: "";
      position: absolute;
      width: 20px;
      height: 20px;
      background: var(--button-hover);
      border-radius: 50%;
      top: 0;
      left: 20px;
      transition: left 0.3s ease;
    }

    #toggle-theme::before {
      content: "";
      position: absolute;
      width: 20px;
      height: 20px;
      background: var(--button-hover);
      border-radius: 50%;
      top: 0;
      left: 0;
      transition: left 0.3s ease;
    }

    /* View Toggle */
    .view-toggle {
      margin-bottom: 20px;
    }

    .view-toggle-btn {
      padding: 8px 16px;
      background-color: var(--button-bg);
      color: var(--white);
      border: none;
      border-radius: 5px;
      cursor: pointer;
      transition: background-color 0.3s ease;
    }

    .view-toggle-btn:hover {
      background-color: var(--button-hover);
    }

    /* Header */
    header h1 {
      font-family: "Salsa", cursive;
      font-size: 2rem;
      margin-bottom: 10px;
      color: var(--text);
    }

    /* About Blurb */
    .about p {
      font-size: 1.1rem;
      margin-bottom: 20px;
      color: var(--text);
    }

    /* Profile Picture */
    .profile-pic img {
      width: 150px;
      height: 150px;
      border-radius: 50%;
      object-fit: cover;
      margin-bottom: 20px;
      border: 3px solid var(--green);
    }

    /* Simple Links Section */
    .simple-links ul {
      list-style: none;
    }

    .simple-links ul li {
      margin: 10px 0;
    }

    .simple-links ul li a {
      display: flex;
      align-items: center;
      gap: 10px;
      padding: 10px;
      background-color: var(--button-bg);
      color: var(--white);
      text-decoration: none;
      border-radius: 5px;
      transition: background-color 0.3s ease;
    }

    .simple-links ul li a:hover {
      background-color: var(--button-hover);
    }

    .simple-links ul li a i {
      font-size: 1.2rem;
    }

    /* Expanded Links Section */
    .expanded-links {
      display: none;
    }

    .expanded-links .link-item {
      display: flex;
      align-items: center;
      gap: 20px;
      margin-bottom: 30px;
      padding: 20px;
      background-color: rgba(0, 0, 0, 0.05);
      border-radius: 10px;
      transition: transform 0.3s ease;
    }

    .expanded-links .link-item:hover {
      transform: translateY(-5px);
    }

    .expanded-links .link-item.reverse {
      flex-direction: row-reverse;
    }

    .link-image img {
      width: auto;
      height: 100px;
      border-radius: 10px;
      object-fit: cover;
    }

    .link-content {
      flex: 1;
      text-align: left;
    }

    .link-content h2 {
      font-family: "Salsa", cursive;
      font-size: 1.5rem;
      margin-bottom: 10px;
      color: var(--text);
    }

    .link-content p {
      font-size: 1rem;
      margin-bottom: 15px;
      color: var(--text);
    }

    .link-button {
      display: inline-block;
      padding: 10px 20px;
      background-color: var(--button-bg);
      color: var(--white);
      text-decoration: none;
      border-radius: 5px;
      transition: background-color 0.3s ease;
    }

    .link-button:hover {
      background-color: var(--button-hover);
    }


    
    /* Social Media Handles */
    .social-media {
      margin-top: 40px;
    }

    .social-media h2 {
      font-family: "Salsa", cursive;
      font-size: 1.8rem;
      margin-bottom: 20px;
      color: var(--text);
    }

    .social-links {
      display: flex;
      justify-content: center;
      gap: 20px;
      flex-wrap: wrap;
    }

    .social-links a {
      display: flex;
      align-items: center;
      gap: 10px;
      padding: 10px 20px;
      background-color: var(--button-bg);
      color: var(--white);
      text-decoration: none;
      border-radius: 5px;
      transition: background-color 0.3s ease;
    }

    .social-links a:hover {
      background-color: var(--button-hover);
    }

    .social-links a i {
      font-size: 1.2rem;
    }

    /* Footer */
    footer {
      margin-top: 20px;
      font-size: 0.9rem;
      color: var(--text);
    }

    /* Mobile Responsiveness */
    @media (max-width: 600px) {
      header h1 {
        font-size: 2rem;
      }

      .about p {
        font-size: 1rem;
      }

      .profile-pic img {
        width: 120px;
        height: 120px;
      }

      .simple-links ul li a {
        font-size: 0.9rem;
      }

      .expanded-links .link-item {
        flex-direction: column;
        text-align: center;
      }

      .link-content {
        text-align: center;
      }

      .social-links {
        flex-direction: column;
        gap: 10px;
      }
    }

    /* Modal Styles */
    .modal {
      display: none;
      position: fixed;
      z-index: 1000;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
    }

    .modal-content {
      background-color: var(--background);
      margin: 5% auto;
      padding: 30px;
      border-radius: 10px;
      width: 90%;
      max-width: 600px;
      max-height: 80vh;
      overflow-y: auto;
      position: relative;
    }

    .close {
      color: var(--text);
      float: right;
      font-size: 28px;
      font-weight: bold;
      cursor: pointer;
      position: absolute;
      top: 15px;
      right: 20px;
    }

    .close:hover {
      opacity: 0.7;
    }

    .modal h2 {
      color: var(--text);
      margin-bottom: 20px;
    }

    .modal p {
      color: var(--text);
      line-height: 1.6;
      margin-bottom: 15px;
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- Light/Dark Mode Toggle -->
    <div class="theme-toggle">
      <input type="checkbox" id="toggle-theme">
      <label for="toggle-theme" class="toggle-label">
        <i class="fas fa-sun"></i>
        <i class="fas fa-moon"></i>
      </label>
    </div>

    <!-- View Toggle -->
    <div class="view-toggle">
      <button id="view-toggle-btn" class="view-toggle-btn">Switch to Expanded View</button>
    </div>

    <!-- Welcome Header -->
    <header>
      <h1 id="header-title">Welcome to the Chip-to-net!</h1>
    </header>

    <!-- About Blurb -->
    <section class="about">
      <p>Hi! I'm A. A. Chips, and this is my corner of the internet. Here, you'll find links to my projects, blogs, and more. Thanks for stopping by!</p>
    </section>

    <!-- Profile Picture -->
    <div class="profile-pic">
      <img src="./assets/selfie1.jpg" alt="A. A. Chips" width="150" height="150">
    </div>
 <!-- Simple Links Section -->
    <section class="simple-links">
      <ul>
        <li><a href="#" id="welcome-modal-btn"><i class="fas fa-info-circle"></i> Welcome - Read Me!</a></li>
        <li><a href="https://www.aachips.co/order.php" target="_blank"><i class="fas fa-gift"></i> Apple Chip Holiday Orders</a></li>
        <li><a href="https://aachips.co/why-chip.php" target="_blank"><i class="fas fa-apple-alt"></i> Apple Facts & Why Chip?</a></li>
        <li><a href="https://www.aachips.co/diy-guide.html" target="_blank"><i class="fas fa-hammer"></i> Make your own Apple Chips - DIY Guide</a></li>
        <li><a href="https://aachips.co/blog" target="_blank"><i class="fas fa-pencil-alt"></i> Apple Chip Blog</a></li>
        <li><a href="https://aachips.co/assessment" target="_blank"><i class="fas fa-clipboard-check"></i> Assessment for Adult Competencies</a></li>
        <li><a href="https://aachips.co/heartwarmers-pr" target="_blank"><i class="fas fa-heart"></i> Heartwarmers Project</a></li>
        <li><a href="https://aachips.co/lingua" target="_blank"><i class="fas fa-utensils"></i> Kitchen Lingua</a></li>
        <li><a href="https://ko-fi.com/aachips" target="_blank"><i class="fas fa-coffee"></i> Ko-fi Crowdfunding Campaign</a></li>
        <li><a href="https://aachips.co/support" target="_blank"><i class="fas fa-gift"></i> Support Wishlist and Goals</a></li>
        <li><a href="https://aachips.co/hire" target="_blank"><i class="fas fa-briefcase"></i> Hire A. A. Chips</a></li>
        <li><a href="https://aachips.co/portfolio" target="_blank"><i class="fas fa-folder-open"></i> Portfolio</a></li>
        <li><a href="https://aachips.co/obsidian-quartz/gallery.php" target="_blank"><i class="fas fa-images"></i> Gallery</a></li>
        <li><a href="store.html" target="_blank"><i class="fas fa-shopping-bag"></i> Store</a></li>
      </ul>
    </section>

    <!-- Expanded Links Section -->
    <section class="expanded-links">
      <div class="link-item">
        <div class="link-image">
          <img src="assets/chips/Chips-bowl.png" alt="Welcome Preview">
        </div>
        <div class="link-content">
          <h2>👋 Welcome - Read Me!</h2>
          <p>Get to know me better and learn about my journey with apple chips and all my projects.</p>
          <a href="#" id="welcome-modal-btn-expanded" class="link-button">Read Introduction</a>
        </div>
      </div>

      <div class="link-item reverse">
        <div class="link-image">
          <img src="assets/chips/Chips-Pile.png" alt="Holiday Orders Preview">
        </div>
        <div class="link-content">
          <h2>🎁 Apple Chip Holiday Orders</h2>
          <p>Order fresh, homemade apple chips for the holidays! Perfect for gifts or treating yourself.</p>
          <a href="https://www.aachips.co/order.php" target="_blank" class="link-button">Place Order</a>
        </div>
      </div>

      <div class="link-item">
        <div class="link-image">
          <img src="assets/chips/Chips-bowl.png" alt="Apple Facts Preview">
        </div>
        <div class="link-content">
          <h2>🍎 Apple Facts & Why Chip?</h2>
          <p>Discover fascinating facts about apples and why they're so important to me. Learn about the history, varieties, and health benefits of apples.</p>
          <a href="https://aachips.co/why-chip.php" target="_blank" class="link-button">Visit Page</a>
        </div>
      </div>

      <div class="link-item reverse">
        <div class="link-image">
          <img src="assets/chips/Chips-Pile.png" alt="DIY Guide Preview">
        </div>
        <div class="link-content">
          <h2>🔨 Make your own Apple Chips - DIY Guide</h2>
          <p>Learn how to make delicious apple chips at home with my step-by-step guide and tips.</p>
          <a href="https://www.aachips.co/diy-guide.html" target="_blank" class="link-button">View Guide</a>
        </div>
      </div>

      <div class="link-item reverse">
        <div class="link-image">
          <img src="assets/logo125.png" alt="AAChips logo.">
        </div>
        <div class="link-content">
          <h2>📝 Apple Chip Blog</h2>
          <p>Read my latest thoughts on apple chips, recipes, and more. A blog dedicated to all things apple chips!</p>
          <a href="https://aachips.co/blog" target="_blank" class="link-button">Visit Blog</a>
        </div>
      </div>

      <div class="link-item">
        <div class="link-image">
          <img src="assessment/assets/logos/compass.png" alt="Assessment for Adult Competencies Preview">
        </div>
        <div class="link-content">
          <h2>Assessment for Adult Competencies</h2>
          <p>Discover your strengths and weaknesses in adulting skills with this free tool.</p>
          <a href="https://aachips.co/assessment" target="_blank" class="link-button">Visit Page</a>
        </div>
      </div>

      <div class="link-item reverse">
        <div class="link-image">
          <img src="heartwarmers/assets/heartwarmer-logo-svg.svg" alt="Heartwarmers Project Preview">
        </div>
        <div class="link-content">
          <h2>Heartwarmers Project</h2>
          <p>An interactive map and database of free offerings for at-risk individuals, focused on homeless advocacy.</p>
          <a href="https://aachips.co/heartwarmers-pr" target="_blank" class="link-button">Visit Page</a>
        </div>
      </div>

      <div class="link-item">
        <div class="link-image">
          <img src="assets/kitchenlinguacoin.pngs" alt="Kitchen Lingua Logo">
        </div>
        <div class="link-content">
          <h2>Kitchen Lingua</h2>
          <p>A platform for sharing Sephardic Spanish language learning and food content.</p>
          <a href="https://aachips.co/lingua" target="_blank" class="link-button">Visit Page</a>
        </div>
      </div>

      <div class="link-item reverse">
        <div class="link-image">
          <img src="assets/icons/Ko-fi_COIN.gif" alt="Ko-fi Logo">
        </div>
        <div class="link-content">
          <h2>Ko-fi Crowdfunding Campaign</h2>
          <p>Help support my work by buying me a coffee (or two, or three...).</p>
          <a href="https://ko-fi.com/aachips" target="_blank" class="link-button">Visit Page</a>
        </div>
      </div>

      <div class="link-item">
        <div class="link-image">
          <img src="assets/images/30degrees.png" alt="Chips on hammock resting.">
        </div>
        <div class="link-content">
          <h2>Support Wishlist and Goals</h2>
          <p>See how you can support me and my work.</p>
          <a href="support.html" target="_blank" class="link-button">Visit Page</a>
        </div>
      </div>

      <div class="link-item reverse">
        <div class="link-image">
            <img src="assets/self/fotor10.jpg" alt="A. A. Chips">
        </div>
        <div class="link-content">
          <h2>Hire A. A. Chips</h2>
          <p>Need a web developer or writer? Let's chat about your project.</p>
          <a href="hire.html" target="_blank" class="link-button">Visit Page</a>
        </div>
      </div>

      <div class="link-item">
        <div class="link-image">
          <img src="assets/images/colorboard.jpg" alt="Communication Color Board.">
        </div>
        <div class="link-content">
          <h2>Portfolio</h2>
          <p>See my previous work and projects.</p>
          <a href="portfolio.html" target="_blank" class="link-button">Visit Page</a>
        </div>
      </div>

      <div class="link-item reverse">
        <div class="link-image">
          <img src="assets/banksy.jpg" alt="Banksy art TV thrown out of window.">
        </div>
        <div class="link-content">
          <h2>Gallery</h2>
          <p>See some of my favorite photos.</p>
          <a href="https://aachips.co/obsidian-quartz/gallery.php" target="_blank" class="link-button">Visit Page</a>
        </div>
      </div>

      <div class="link-item">
        <div class="link-image">
          <img src="assets/chips/photoKseniiaChunaeva.jpg" alt="Apple Chips by Kseniia Chunaeva">
        </div>
        <div class="link-content">
          <h2>Store</h2>
          <p>Buy some of my favorite products.</p>
          <a href="store.html" target="_blank" class="link-button">Visit Page</a>
        </div>
      </div>
<!-- Obsidian Quartz -->

      <div class="link-item">
        <div class="link-image">
          <img src="./assets/rosegrandpasvg.svg" alt="Rose Grandpa SVG">
        </div>
        <div class="link-content">
          <h2>Obsidian Quartz</h2>
          <p>Nothing interesting here. Just a bunch of boring things not worth your time. Don't worry about it.</p>
          <a href="https://aachips.co/obsidian-quartz/content/index.php" target="_blank" class="link-button">Visit Page</a>
        </div>
      </div>
    </section>

    <!-- Social Media Handles -->
    <section class="social-media">
      <h2>Connect With Me</h2>
      <div class="social-links">
        <a href="https://bsky.app/profile/aachips.bsky.social" target="_blank"><i class="fab fa-bluesky"></i> Bluesky</a>
        <a href="https://github.com/aachips" target="_blank"><i class="fab fa-github"></i> GitHub</a>
        <a href="https://discord.com/users/aprilaplcyr" target="_blank"><i class="fab fa-discord"></i> Discord</a>
        <a href="https://ko-fi.com/aachips" target="_blank"><i class="fas fa-coffee"></i> Ko-fi</a>
      </div>
    </section>

    <!-- Optional Footer -->
    <footer>
      <p>© 2023 A. A. Chips. All rights reserved.</p>
    </footer>
  </div>

  <!-- Welcome Modal -->
  <div id="welcome-modal" class="modal">
    <div class="modal-content">
      <span class="close">&times;</span>
      <h2>Welcome to A. A. Chips' Corner of the Internet!</h2>
      <p>Hi there! I'm A. A. Chips, and I'm so glad you found your way here. This little corner of the internet is where I share my passion for apple chips, my various projects, and connect with amazing people like you.</p>
      
      <p>What started as a simple love for apple chips has grown into something much bigger. Through my journey, I've discovered the joy of creating, sharing knowledge, and building communities that support one another.</p>
      
      <p>Here you'll find everything from my apple chip business and DIY guides, to advocacy projects like Heartwarmers, tech coaching, and so much more. Each link tells a part of my story and represents something I'm passionate about.</p>
      
      <p>Feel free to explore, reach out, and don't hesitate to say hello! I love connecting with new people and hearing about your own journeys.</p>
      
      <p>Thanks for being here! 🍎✨</p>
    </div>
  </div>

  <script>
    // Theme Toggle
    const toggle = document.getElementById('toggle-theme');
    const body = document.body;

    toggle.addEventListener('change', () => {
      body.setAttribute('data-theme', toggle.checked ? 'dark' : 'light');
    });

    // View Toggle
    const viewToggleBtn = document.getElementById('view-toggle-btn');
    const simpleLinks = document.querySelector('.simple-links');
    const expandedLinks = document.querySelector('.expanded-links');
    const headerTitle = document.getElementById('header-title');

    viewToggleBtn.addEventListener('click', () => {
      if (simpleLinks.style.display === 'none') {
        // Switch to simple view
        simpleLinks.style.display = 'block';
        expandedLinks.style.display = 'none';
        viewToggleBtn.textContent = 'Switch to Expanded View';
        headerTitle.textContent = 'Welcome to the Chip-to-net!';
      } else {
        // Switch to expanded view
        simpleLinks.style.display = 'none';
        expandedLinks.style.display = 'block';
        viewToggleBtn.textContent = 'Switch to Simple View';
        headerTitle.textContent = 'Welcome to the Chip-to-net!';
      }
    });

    // Initialize with simple view
    simpleLinks.style.display = 'block';
    expandedLinks.style.display = 'none';

    // Modal functionality
    const modal = document.getElementById('welcome-modal');
    const welcomeBtns = [
      document.getElementById('welcome-modal-btn'),
      document.getElementById('welcome-modal-btn-expanded')
    ];
    const closeBtn = document.querySelector('.close');

    welcomeBtns.forEach(btn => {
      if (btn) {
        btn.addEventListener('click', (e) => {
          e.preventDefault();
          modal.style.display = 'block';
        });
      }
    });

    closeBtn.addEventListener('click', () => {
      modal.style.display = 'none';
    });

    window.addEventListener('click', (e) => {
      if (e.target === modal) {
        modal.style.display = 'none';
      }
    });
  </script>
</body>
</html>
