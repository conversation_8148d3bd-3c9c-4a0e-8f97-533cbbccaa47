# School-Themed Website CSS Layout

This is a mobile-first CSS layout designed specifically for school-themed websites. It features a parchment-like background, CSS Grid for the main layout structure, and Flexbox for card-based blog entries.

## Features

- **Mobile-First Design**: Optimized for mobile devices with responsive breakpoints for larger screens
- **CSS Grid Layout**: Used for the main page structure (header, content, sidebar, footer)
- **Flexbox Card Layout**: Used for blog entries and content cards
- **Parchment-Like Background**: Subtle texture background for a school/academic feel
- **Interactive Navigation**: With hover effects and mobile-friendly toggle
- **School-Themed Color Palette**: Maroon, parchment tan, and deep blue
- **Typography**: Uses Google Fonts (Playfair Display for headings, Raleway for body text)
- **Responsive Breakpoints**: Mobile, tablet (768px+), desktop (992px+), and large desktop (1200px+)

## How to Use

### 1. Include the Required Files

First, include the Google Fonts and the CSS file in your HTML:

```html
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&family=Raleway:wght@400;500;700&display=swap" rel="stylesheet">
<link rel="stylesheet" href="assets/css/school-theme.css">
```

### 2. Basic Page Structure

Use this basic structure for your pages:

```html
<div class="site-container">
  <!-- Header -->
  <header class="site-header">
    <!-- Logo and site title -->
    <div class="site-logo">
      <img src="path/to/logo.png" alt="School Logo">
      <h1 class="site-title">School Name</h1>
    </div>
    
    <!-- Navigation -->
    <nav class="site-navigation">
      <button class="nav-toggle js-nav-toggle">Menu</button>
      <ul class="nav-menu js-nav-menu">
        <li><a href="#" class="active">Home</a></li>
        <li><a href="#">Link 2</a></li>
        <li><a href="#">Link 3</a></li>
      </ul>
    </nav>
  </header>
  
  <!-- Main Content -->
  <main class="site-main">
    <!-- Your main content here -->
  </main>
  
  <!-- Sidebar -->
  <aside class="site-sidebar">
    <!-- Sidebar widgets here -->
  </aside>
  
  <!-- Footer -->
  <footer class="site-footer">
    <!-- Footer content here -->
  </footer>
</div>
```

### 3. Blog Cards

To create blog cards, use this structure:

```html
<div class="blog-container">
  <article class="blog-card">
    <img src="path/to/image.jpg" alt="Blog Image" class="blog-card-image">
    <div class="blog-card-content">
      <h3 class="blog-card-title">Article Title</h3>
      <div class="blog-card-meta">
        <span class="blog-card-date">Date</span>
        <div class="blog-card-author">
          <img src="path/to/author.jpg" alt="Author">
          <span>Author Name</span>
        </div>
      </div>
      <div class="blog-card-excerpt">
        <p>Article excerpt or summary...</p>
      </div>
      <div class="blog-card-footer">
        <div class="blog-card-categories">
          <span class="blog-card-category">Category 1</span>
          <span class="blog-card-category">Category 2</span>
        </div>
        <a href="#" class="blog-card-link">Read More</a>
      </div>
    </div>
  </article>
  <!-- More blog cards as needed -->
</div>
```

### 4. Sidebar Widgets

For sidebar widgets, use this structure:

```html
<div class="widget">
  <h3 class="widget-title">Widget Title</h3>
  <div class="widget-content">
    <!-- Widget content here -->
  </div>
</div>
```

### 5. JavaScript for Mobile Navigation

Include this JavaScript for the mobile navigation toggle:

```html
<script>
  document.addEventListener('DOMContentLoaded', function() {
    const navToggle = document.querySelector('.js-nav-toggle');
    const navMenu = document.querySelector('.js-nav-menu');
    
    navToggle.addEventListener('click', function() {
      navMenu.classList.toggle('active');
    });
  });
</script>
```

## Customization

### Colors

You can customize the colors by modifying the CSS variables in the `:root` section:

```css
:root {
  --primary: #8e3200;         /* Deep maroon/burgundy */
  --primary-light: #a84a00;   /* Lighter maroon */
  --secondary: #d4a76a;       /* Parchment tan */
  --secondary-light: #e9d2b3; /* Light parchment */
  --accent: #1d3557;          /* Deep blue */
  --accent-light: #457b9d;    /* Lighter blue */
  /* ... other variables ... */
}
```

### Typography

You can change the fonts by modifying these variables:

```css
:root {
  --font-heading: 'Playfair Display', Georgia, serif;
  --font-body: 'Raleway', 'Segoe UI', Tahoma, sans-serif;
  /* ... other variables ... */
}
```

### Spacing

Adjust spacing throughout the design with these variables:

```css
:root {
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2.5rem;
  /* ... other variables ... */
}
```

## Browser Compatibility

This CSS layout is compatible with:
- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Opera (latest)
- Mobile browsers (iOS Safari, Android Chrome)

## License

This CSS layout is free to use for both personal and commercial projects.

## Credits

- Background pattern generated with [Hero Patterns](https://www.heropatterns.com/)
- Placeholder images from [Unsplash](https://unsplash.com/)
- Fonts from [Google Fonts](https://fonts.google.com/)
