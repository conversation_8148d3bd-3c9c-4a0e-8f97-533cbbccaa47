/* High Contrast Mode */
body.high-contrast {
    --primary-color: #000000;
    --secondary-color: #333333;
    --text-color: #000000;
    --bg-color: #ffffff;
    --link-color: #0000EE;
    --link-hover: #551A8B;
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* Focus Styles */
a:focus,
button:focus,
input:focus,
select:focus,
textarea:focus {
    outline: 3px solid #0056b3;
    outline-offset: 2px;
}

/* Large Text Mode */
body.large-text {
    --font-size: 20px;
}

body.extra-large-text {
    --font-size: 24px;
}