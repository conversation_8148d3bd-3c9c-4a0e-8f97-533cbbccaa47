@import url('https://fonts.googleapis.com/css2?family=Noto+Serif:ital@1&display=swap');

/******* Background and Wrapper ******/
html {
  font: 17px/1.5 'Trebuchet MS', Helvetica, sans-serif;
}

body {
  background: url(../images/page-bg.jpg) left top/cover;
  margin: 0;
}

#wrapper {
  background-color: #fff;
  box-shadow: 0 0 10px #000;
  margin: 0 auto 2em;
  min-width: 650px;
  padding: 2em;
  width: 80%;
}

/****** Main Navigation *****/
#skip-link {
  background-color: #000;
  border: #fff double 6px;
  color: #fff;
  padding: .5rem;
  text-decoration: none;
}

nav {
  background-color: #333;
  border-radius: 0 10px 10px 0;
  box-shadow: 3px 0 5px #000;
  margin-left: -2rem;
  padding: .25rem;
}

nav li {
  display: inline;
}

nav a {
  border-right: #ddd solid 2px;
  color: #ddd;
  font-weight: bold;
  padding-left: .5rem;
  padding-right: 1rem;
  text-decoration: none;
}

nav a:hover {
  text-decoration: overline;
}

nav li:last-child a {
  border-right: none;
}

#secondnav {
  display: flow-root;
}

#current-page {
  color: #000;
  text-decoration: none;
  text-shadow: 1px 1px 3px #fff,
              1px -1px 3px #fff,
              -1px 1px 3px #fff,
              -1px -1px 3px #fff;
}

/****** Page Content *****/

h1 {
  border-bottom: #333 dashed 1px;
  font: 4rem 'Noto Serif', serif;
  text-align: center;
  text-shadow: -2px -2px 3px #d3d3d3, 
               -3px -3px 0 #a9a9a9;
}

h2 {
  background: url(../images/crane-icon.png) no-repeat;
  font-family: 'Eagle Lake', cursive;
  margin-bottom: 0;
  padding-left: 40px;
}

h2 + p {
  margin-top: 0;
}

main img {
  background-color: #ddd;
  border: 3px solid #000;
  box-shadow: inset 0 0 5px #000, 
              0 0 5px #000;
  padding: 10px;
}

figcaption {
  border: #333 solid 1px;
  border-left-width: 20px;
  border-radius: 25px;
  border-right-width: 20px;
  box-shadow: 0 15px 10px -15px #000;
  margin-top: 5px;
  padding: 5px 0;
  text-align: center;
  width: 586px;
}

small {
  font-style: italic;
}

/****** Thumbnails *****/
.thumbs li {
  display: inline;
  margin-right: 10px;
}

/****** Contact Page *****/

address {
  font-style: normal;
}

form > fieldset > legend {
  font-variant: small-caps;
  font-weight: bold;
}
