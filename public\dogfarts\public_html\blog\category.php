<?php
/**
 * Category Blog Posts Display
 *
 * This script fetches and displays blog posts from a specific category
 * based on the category slug provided in the URL.
 */

// Include necessary files
require_once '../config/config.php';
require_once '../config/db.php';
require_once '../lib/functions.php';
include_once '../includes/header.php';
include_once '../includes/nav.php';

// Get category slug from URL
$category_slug = isset($_GET['slug']) ? $_GET['slug'] : '';

// Initialize variables
$category = null;
$posts = [];
$error = '';

// Validate category slug
if (empty($category_slug)) {
    $error = 'No category specified';
} else {
    try {
        // Get database connection
        $db = getDbConnection();

        // Fetch the category with prepared statement
        $stmt = $db->prepare("
            SELECT id, name, slug, description
            FROM categories
            WHERE slug = :slug
        ");

        $stmt->bindParam(':slug', $category_slug, PDO::PARAM_STR);
        $stmt->execute();
        $category = $stmt->fetch();

        // If category exists, fetch posts in this category
        if ($category) {
            $stmt = $db->prepare("
                SELECT
                    p.id,
                    p.title,
                    p.content,
                    p.date_published,
                    p.author_id,
                    a.name as author_name
                FROM
                    posts p
                JOIN
                    post_categories pc ON p.id = pc.post_id
                LEFT JOIN
                    authors a ON p.author_id = a.id
                WHERE
                    pc.category_id = :category_id
                    AND p.status = 'published'
                ORDER BY
                    p.date_published DESC
            ");

            $stmt->bindParam(':category_id', $category['id'], PDO::PARAM_INT);
            $stmt->execute();
            $posts = $stmt->fetchAll();
        }
    } catch (PDOException $e) {
        // Log the error (in a production environment)
        error_log('Database query error: ' . $e->getMessage());

        // Set error message
        $error = 'Sorry, we could not retrieve the requested category.';
    }
}
?>

<main class="blog-category">
    <?php if ($error): ?>
        <div class="error-message">
            <?php echo htmlspecialchars($error); ?>
            <p><a href="index.php">Return to blog listing</a></p>
        </div>
    <?php elseif (!$category): ?>
        <div class="not-found">
            <h1>Category Not Found</h1>
            <p>The requested category could not be found.</p>
            <p><a href="index.php">Return to blog listing</a></p>
        </div>
    <?php else: ?>
        <h1>Category: <?php echo htmlspecialchars($category['name']); ?></h1>

        <?php if (!empty($category['description'])): ?>
            <div class="category-description">
                <?php echo htmlspecialchars($category['description']); ?>
            </div>
        <?php endif; ?>

        <?php if (empty($posts)): ?>
            <div class="no-posts">
                <p>No posts found in this category.</p>
            </div>
        <?php else: ?>
            <div class="posts-container">
                <?php foreach ($posts as $post): ?>
                    <article class="post">
                        <h2 class="post-title">
                            <a href="post.php?id=<?php echo $post['id']; ?>">
                                <?php echo htmlspecialchars($post['title']); ?>
                            </a>
                        </h2>

                        <div class="post-meta">
                            <span class="post-date">
                                <?php echo formatDate($post['date_published']); ?>
                            </span>

                            <?php if (!empty($post['author_name'])): ?>
                                <span class="post-author">
                                    by <?php echo htmlspecialchars($post['author_name']); ?>
                                </span>
                            <?php endif; ?>
                        </div>

                        <div class="post-excerpt">
                            <?php
                            // Create an excerpt from the content
                            echo createExcerpt($post['content']);
                            ?>
                            <a href="post.php?id=<?php echo $post['id']; ?>" class="read-more">
                                Read More
                            </a>
                        </div>
                    </article>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>

        <div class="category-navigation">
            <a href="index.php" class="back-to-blog">← Back to All Posts</a>
        </div>
    <?php endif; ?>
</main>

<?php
include 'sidebar.php';
include '../includes/footer.php';
?>
