<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Path Test</title>
    
    <!-- Test different CSS paths -->
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="/heartwarmers/css/main.css">
    <link rel="stylesheet" href="/css/main.css">
    <link rel="stylesheet" href="./css/main.css">
    
    <!-- Font Awesome (working) -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    
    <style>
        .test-section {
            margin: 2rem;
            padding: 1rem;
            border: 1px solid #ccc;
        }
        .working { background-color: #d4edda; }
        .not-working { background-color: #f8d7da; }
    </style>
</head>
<body>
    <div class="container">
        <h1>CSS Path Testing</h1>
        
        <div class="test-section">
            <h2>Font Awesome Test (Should Work)</h2>
            <i class="fas fa-heart"></i>
            <i class="fas fa-building"></i>
            <i class="fas fa-hands-helping"></i>
        </div>
        
        <div class="test-section">
            <h2>CSS Variables Test</h2>
            <p>If this text is styled with the correct font and colors, CSS is loading.</p>
            <button class="btn-primary">Primary Button</button>
            <button class="btn-secondary">Secondary Button</button>
        </div>
        
        <div class="test-section">
            <h2>Container Test</h2>
            <p>This should be in a container with max-width and centered.</p>
        </div>
    </div>
    
    <script>
        // Check which CSS files loaded
        const links = document.querySelectorAll('link[rel="stylesheet"]');
        links.forEach((link, index) => {
            const img = new Image();
            img.onload = function() {
                console.log(`CSS ${index + 1} (${link.href}) - LOADED`);
            };
            img.onerror = function() {
                console.log(`CSS ${index + 1} (${link.href}) - FAILED`);
            };
            img.src = link.href;
        });
    </script>
</body>
</html>
