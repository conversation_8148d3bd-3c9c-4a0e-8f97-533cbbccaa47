<?php
/**
 * User-related functions for Heartwarmers website
 */

// Include database connection
require_once 'db.php';

/**
 * Register a new user
 * @param string $username Username
 * @param string $email Email address
 * @param string $password Password (will be hashed)
 * @param string $location User's location
 * @param string $user_type User type (help_seeker, business_organization, volunteer_mutual_aid)
 * @return array|bool Returns user data on success, false on failure
 */
function register_user($username, $email, $password, $location = '', $user_type = 'help_seeker') {
    // Create database if it doesn't exist
    if (!create_database_if_not_exists()) {
        return ['error' => 'Failed to create database. Please check your database configuration.'];
    }

    // Connect to the database
    $conn = get_db_connection();

    if (!$conn) {
        return ['error' => 'Database connection failed. Please try again later.'];
    }

    // Create tables using the SQL script
    try {
        // Read the SQL file
        $sql_path = __DIR__ . '/../../setup_database.sql';

        if (file_exists($sql_path)) {
            $sql = file_get_contents($sql_path);

            // Split SQL by semicolon
            $queries = explode(';', $sql);

            // Execute each query
            foreach ($queries as $query) {
                $query = trim($query);
                if (!empty($query)) {
                    // Skip the CREATE DATABASE and USE statements
                    if (strpos($query, 'CREATE DATABASE') === false && strpos($query, 'USE') === false) {
                        $conn->query($query);
                    }
                }
            }
        }
    } catch (Exception $e) {
        error_log("Error creating tables: {$e->getMessage()}");
        // Continue anyway - tables might already exist
    }

    // Check if email already exists
    $stmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
    if (!$stmt) {
        return ['error' => 'Database error: ' . $conn->error];
    }

    $stmt->bind_param("s", $email);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        return ['error' => 'Email already registered'];
    }

    // Generate slug from username
    $slug = generate_unique_slug($username);

    // Hash password
    $hashed_password = password_hash($password, PASSWORD_DEFAULT);

    // Validate user type
    $valid_types = ['help_seeker', 'business_organization', 'volunteer_mutual_aid'];
    if (!in_array($user_type, $valid_types)) {
        $user_type = 'help_seeker';
    }

    // Check if user_type column exists, if not add it
    $check_column = $conn->query("SHOW COLUMNS FROM users LIKE 'user_type'");
    if ($check_column->num_rows == 0) {
        // Add the user_type column
        $alter_sql = "ALTER TABLE users ADD COLUMN user_type ENUM('help_seeker', 'business_organization', 'volunteer_mutual_aid') DEFAULT 'help_seeker' AFTER slug";
        $conn->query($alter_sql);
    }

    // Insert new user
    $stmt = $conn->prepare("INSERT INTO users (username, email, password, slug, user_type, location) VALUES (?, ?, ?, ?, ?, ?)");

    if ($stmt === false) {
        return ['error' => 'Database error: ' . $conn->error];
    }

    $stmt->bind_param("ssssss", $username, $email, $hashed_password, $slug, $user_type, $location);

    if ($stmt->execute()) {
        $user_id = $conn->insert_id;

        // Create default sections based on user type
        create_default_sections($user_id, $user_type);

        // Get user data
        $user = get_user_by_id($user_id);
        return $user;
    } else {
        return ['error' => 'Registration failed: ' . $conn->error];
    }
}

/**
 * Create default profile sections for a new user based on their type
 * @param int $user_id User ID
 * @param string $user_type User type
 * @return bool Success or failure
 */
function create_default_sections($user_id, $user_type = 'help_seeker') {
    $conn = get_db_connection();

    if (!$conn) {
        return false;
    }

    // Define different default sections based on user type
    switch ($user_type) {
        case 'business_organization':
            $default_sections = [
                ['About Our Organization', 'Tell people about your business or organization...', 1],
                ['Services & Offerings', 'What services or support do you provide?', 2],
                ['How to Get Involved', 'How can people work with or support your organization?', 3],
                ['Contact Information', 'How people can reach you...', 4]
            ];
            break;

        case 'volunteer_mutual_aid':
            $default_sections = [
                ['About Me', 'Tell people about yourself and your volunteer work...', 1],
                ['Skills & Services', 'What skills or services can you offer?', 2],
                ['Availability', 'When and how are you available to help?', 3],
                ['Contact Information', 'How people can reach you...', 4]
            ];
            break;

        case 'help_seeker':
        default:
            $default_sections = [
                ['About Me', 'Tell people about yourself...', 1],
                ['My Story', 'Share your journey...', 2],
                ['Frequently Asked Questions', 'Answer common questions...', 3],
                ['Contact Information', 'How people can reach you...', 4]
            ];
            break;
    }

    $stmt = $conn->prepare("INSERT INTO user_sections (user_id, title, content, display_order) VALUES (?, ?, ?, ?)");

    foreach ($default_sections as $section) {
        $stmt->bind_param("issi", $user_id, $section[0], $section[1], $section[2]);
        $stmt->execute();
    }

    return true;
}

/**
 * Generate a unique slug from username
 * @param string $username Username
 * @return string Unique slug
 */
function generate_unique_slug($username) {
    $conn = get_db_connection();

    // Convert username to lowercase and replace spaces with hyphens
    $slug = generate_slug($username);


    // Check if slug exists
    $original_slug = $slug;
    $i = 1;

    while (true) {
        $stmt = $conn->prepare("SELECT id FROM users WHERE slug = ?");
        $stmt->bind_param("s", $slug);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows == 0) {
            break;
        }

        // Add number to slug
        $slug = $original_slug . '-' . $i;
        $i++;
    }

    return $slug;
}

/**
 * Authenticate user
 * @param string $email Email address
 * @param string $password Password
 * @return array|bool Returns user data on success, false on failure
 */
function login_user($email, $password) {
    $conn = get_db_connection();

    if (!$conn) {
        return false;
    }

    // Get user by email
    $stmt = $conn->prepare("SELECT id, username, email, password FROM users WHERE email = ?");
    $stmt->bind_param("s", $email);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows == 0) {
        return ['error' => 'Invalid email or password'];
    }

    $user = $result->fetch_assoc();

    // Verify password
    if (password_verify($password, $user['password'])) {
        // Remove password from user data
        unset($user['password']);

        // Start session and store user data
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }

        $_SESSION['user'] = $user;

        return $user;
    } else {
        return ['error' => 'Invalid email or password'];
    }
}

/**
 * Log out user
 * @return bool Success or failure
 */
function logout_user() {
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }

    // Unset user session data
    unset($_SESSION['user']);

    // Destroy session
    session_destroy();

    return true;
}

/**
 * Check if user is logged in
 * @return bool True if logged in, false otherwise
 */
function is_logged_in() {
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }

    return isset($_SESSION['user']);
}

/**
 * Get current logged in user
 * @return array|null User data or null if not logged in
 */
function get_logged_in_user() {
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }

    return isset($_SESSION['user']) ? $_SESSION['user'] : null;
}

/**
 * Get user by ID
 * @param int $user_id User ID
 * @return array|bool User data or false if not found
 */
function get_user_by_id($user_id) {
    $conn = get_db_connection();

    if (!$conn) {
        return false;
    }

    // Check if user_type column exists
    $check_column = $conn->query("SHOW COLUMNS FROM users LIKE 'user_type'");
    $has_user_type = ($check_column->num_rows > 0);

    if ($has_user_type) {
        $stmt = $conn->prepare("SELECT id, username, email, slug, user_type, location, bio, profile_image, banner_image, contact_info, donation_info, created_at FROM users WHERE id = ?");
    } else {
        $stmt = $conn->prepare("SELECT id, username, email, slug, location, bio, profile_image, banner_image, contact_info, donation_info, created_at FROM users WHERE id = ?");
    }

    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows == 0) {
        return false;
    }

    $user_data = $result->fetch_assoc();

    // Add default user_type if column doesn't exist
    if (!$has_user_type) {
        $user_data['user_type'] = 'help_seeker';
    }

    return $user_data;
}

/**
 * Get user by slug
 * @param string $slug User slug
 * @return array|bool User data or false if not found
 */
function get_user_by_slug($slug) {
    $conn = get_db_connection();

    if (!$conn) {
        return false;
    }

    // Check if user_type column exists
    $check_column = $conn->query("SHOW COLUMNS FROM users LIKE 'user_type'");
    $has_user_type = ($check_column->num_rows > 0);

    if ($has_user_type) {
        $stmt = $conn->prepare("SELECT id, username, email, slug, user_type, location, bio, profile_image, banner_image, contact_info, donation_info, created_at FROM users WHERE slug = ?");
    } else {
        $stmt = $conn->prepare("SELECT id, username, email, slug, location, bio, profile_image, banner_image, contact_info, donation_info, created_at FROM users WHERE slug = ?");
    }

    $stmt->bind_param("s", $slug);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows == 0) {
        return false;
    }

    $user_data = $result->fetch_assoc();

    // Add default user_type if column doesn't exist
    if (!$has_user_type) {
        $user_data['user_type'] = 'help_seeker';
    }

    return $user_data;
}

/**
 * Update user profile
 * @param int $user_id User ID
 * @param array $data Profile data to update
 * @return bool Success or failure
 */
function update_user_profile($user_id, $data) {
    $conn = get_db_connection();

    if (!$conn) {
        return false;
    }

    // Build update query
    $updates = [];
    $params = [];
    $types = "";

    $allowed_fields = ['username', 'location', 'bio', 'profile_image', 'banner_image', 'contact_info', 'donation_info', 'privacy_settings'];

    foreach ($allowed_fields as $field) {
        if (isset($data[$field])) {
            $updates[] = "$field = ?";
            $params[] = $data[$field];
            $types .= "s";
        }
    }

    if (empty($updates)) {
        return true; // Nothing to update
    }

    $sql = "UPDATE users SET " . implode(", ", $updates) . " WHERE id = ?";
    $params[] = $user_id;
    $types .= "i";

    $stmt = $conn->prepare($sql);
    $stmt->bind_param($types, ...$params);

    return $stmt->execute();
}

/**
 * Get user profile sections
 * @param int $user_id User ID
 * @return array Sections
 */
function get_user_sections($user_id) {
    $conn = get_db_connection();

    if (!$conn) {
        return [];
    }

    $stmt = $conn->prepare("SELECT id, title, content, display_order, is_visible FROM user_sections WHERE user_id = ? ORDER BY display_order ASC");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();

    $sections = [];
    while ($row = $result->fetch_assoc()) {
        $sections[] = $row;
    }

    return $sections;
}

/**
 * Update user section
 * @param int $section_id Section ID
 * @param array $data Section data
 * @return bool Success or failure
 */
function update_user_section($section_id, $data) {
    $conn = get_db_connection();

    if (!$conn) {
        return false;
    }

    $title = $data['title'];
    $content = $data['content'];
    $is_visible = isset($data['is_visible']) ? (int)$data['is_visible'] : 1;

    $stmt = $conn->prepare("UPDATE user_sections SET title = ?, content = ?, is_visible = ? WHERE id = ?");
    $stmt->bind_param("ssii", $title, $content, $is_visible, $section_id);

    return $stmt->execute();
}

/**
 * Add user section
 * @param int $user_id User ID
 * @param string $title Section title
 * @param string $content Section content
 * @return int|bool New section ID or false on failure
 */
function add_user_section($user_id, $title, $content) {
    $conn = get_db_connection();

    if (!$conn) {
        return false;
    }

    // Get max display order
    $stmt = $conn->prepare("SELECT MAX(display_order) as max_order FROM user_sections WHERE user_id = ?");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    $display_order = ($row['max_order'] ?? 0) + 1;

    // Insert new section
    $stmt = $conn->prepare("INSERT INTO user_sections (user_id, title, content, display_order) VALUES (?, ?, ?, ?)");
    $stmt->bind_param("issi", $user_id, $title, $content, $display_order);

    if ($stmt->execute()) {
        return $conn->insert_id;
    } else {
        return false;
    }
}

/**
 * Delete user section
 * @param int $section_id Section ID
 * @return bool Success or failure
 */
function delete_user_section($section_id) {
    $conn = get_db_connection();

    if (!$conn) {
        return false;
    }

    $stmt = $conn->prepare("DELETE FROM user_sections WHERE id = ?");
    $stmt->bind_param("i", $section_id);

    return $stmt->execute();
}

/**
 * Get user wishlist
 * @param int $user_id User ID
 * @return array Wishlist items
 */
function get_user_wishlist($user_id) {
    $conn = get_db_connection();

    if (!$conn) {
        return [];
    }

    $stmt = $conn->prepare("SELECT id, title, description, priority, status, image, price, url, fulfilled_by, fulfilled_at, created_at FROM wishlist_items WHERE user_id = ? ORDER BY priority ASC, created_at DESC");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();

    $wishlist = [];
    while ($row = $result->fetch_assoc()) {
        $wishlist[] = $row;
    }

    return $wishlist;
}

/**
 * Upload wishlist item image
 * @param array $file Uploaded file data
 * @return array|bool Success or failure with message
 */
function upload_wishlist_image($file) {
    // Include upload functions
    require_once 'upload-functions.php';

    // Upload the image with resizing
    $upload_result = upload_image(
        $file,
        'uploads/wishlist_images',
        ['image/jpeg', 'image/png', 'image/gif'],
        5242880,
        true,  // Enable resizing
        600,   // Max width
        400    // Max height
    );

    if (isset($upload_result['error'])) {
        return ['error' => $upload_result['error']];
    }

    // Get the image path
    $image_path = $upload_result['path'];

    return ['success' => 'Image uploaded successfully', 'path' => $image_path];
}

/**
 * Add wishlist item
 * @param int $user_id User ID
 * @param array $data Item data
 * @return int|bool New item ID or false on failure
 */
function add_wishlist_item($user_id, $data) {
    $conn = get_db_connection();

    if (!$conn) {
        return false;
    }

    $title = $data['title'];
    $description = $data['description'] ?? '';
    $priority = $data['priority'] ?? 3;
    $image = $data['image'] ?? '';
    $price = $data['price'] ?? null;
    $url = $data['url'] ?? '';

    $stmt = $conn->prepare("INSERT INTO wishlist_items (user_id, title, description, priority, image, price, url) VALUES (?, ?, ?, ?, ?, ?, ?)");
    $stmt->bind_param("ississs", $user_id, $title, $description, $priority, $image, $price, $url);

    if ($stmt->execute()) {
        return $conn->insert_id;
    } else {
        return false;
    }
}

/**
 * Update wishlist item
 * @param int $item_id Item ID
 * @param array $data Item data
 * @return bool Success or failure
 */
function update_wishlist_item($item_id, $data) {
    $conn = get_db_connection();

    if (!$conn) {
        return false;
    }

    // Build update query
    $updates = [];
    $params = [];
    $types = "";

    $allowed_fields = ['title', 'description', 'priority', 'status', 'image', 'price', 'url'];

    foreach ($allowed_fields as $field) {
        if (isset($data[$field])) {
            $updates[] = "$field = ?";
            $params[] = $data[$field];
            $types .= ($field == 'priority' || $field == 'price') ? "d" : "s";
        }
    }

    if (empty($updates)) {
        return true; // Nothing to update
    }

    $sql = "UPDATE wishlist_items SET " . implode(", ", $updates) . " WHERE id = ?";
    $params[] = $item_id;
    $types .= "i";

    $stmt = $conn->prepare($sql);
    $stmt->bind_param($types, ...$params);

    return $stmt->execute();
}

/**
 * Delete wishlist item
 * @param int $item_id Item ID
 * @return bool Success or failure
 */
function delete_wishlist_item($item_id) {
    $conn = get_db_connection();

    if (!$conn) {
        return false;
    }

    $stmt = $conn->prepare("DELETE FROM wishlist_items WHERE id = ?");
    $stmt->bind_param("i", $item_id);

    return $stmt->execute();
}

/**
 * Mark wishlist item as fulfilled
 * @param int $item_id Item ID
 * @param int $fulfilled_by User ID of fulfiller (optional)
 * @return bool Success or failure
 */
function fulfill_wishlist_item($item_id, $fulfilled_by = null) {
    $conn = get_db_connection();

    if (!$conn) {
        return false;
    }

    if ($fulfilled_by) {
        $stmt = $conn->prepare("UPDATE wishlist_items SET status = 'fulfilled', fulfilled_by = ?, fulfilled_at = NOW() WHERE id = ?");
        $stmt->bind_param("ii", $fulfilled_by, $item_id);
    } else {
        $stmt = $conn->prepare("UPDATE wishlist_items SET status = 'fulfilled', fulfilled_at = NOW() WHERE id = ?");
        $stmt->bind_param("i", $item_id);
    }

    return $stmt->execute();
}

/**
 * Get user posts
 * @param int $user_id User ID
 * @param int $limit Number of posts to return (0 for all)
 * @return array Posts
 */
function get_user_posts($user_id, $limit = 0) {
    $conn = get_db_connection();

    if (!$conn) {
        return [];
    }

    // Check if new columns exist, if not, use basic query
    $columns_check = $conn->query("SHOW COLUMNS FROM user_posts LIKE 'video_url'");
    $has_new_columns = $columns_check && $columns_check->num_rows > 0;

    if ($has_new_columns) {
        $sql = "SELECT id, content, image, video_url, link_url, link_title, is_pinned, allow_comments, created_at FROM user_posts WHERE user_id = ? AND is_visible = 1 ORDER BY is_pinned DESC, created_at DESC";
    } else {
        $sql = "SELECT id, content, image, is_pinned, created_at FROM user_posts WHERE user_id = ? AND is_visible = 1 ORDER BY is_pinned DESC, created_at DESC";
    }

    if ($limit > 0) {
        $sql .= " LIMIT ?";
        $stmt = $conn->prepare($sql);
        if (!$stmt) {
            error_log("SQL prepare failed: " . $conn->error);
            return [];
        }
        $stmt->bind_param("ii", $user_id, $limit);
    } else {
        $stmt = $conn->prepare($sql);
        if (!$stmt) {
            error_log("SQL prepare failed: " . $conn->error);
            return [];
        }
        $stmt->bind_param("i", $user_id);
    }

    if (!$stmt->execute()) {
        error_log("SQL execute failed: " . $stmt->error);
        return [];
    }

    $result = $stmt->get_result();

    $posts = [];
    while ($row = $result->fetch_assoc()) {
        // Add default values for missing columns
        if (!$has_new_columns) {
            $row['video_url'] = '';
            $row['link_url'] = '';
            $row['link_title'] = '';
            $row['allow_comments'] = 1;
        }
        $posts[] = $row;
    }

    return $posts;
}

/**
 * Add user post
 * @param int $user_id User ID
 * @param string $content Post content
 * @param string $image Post image (optional)
 * @return int|bool New post ID or false on failure
 */
function add_user_post($user_id, $content, $image = '') {
    $conn = get_db_connection();

    if (!$conn) {
        return false;
    }

    $stmt = $conn->prepare("INSERT INTO user_posts (user_id, content, image) VALUES (?, ?, ?)");
    $stmt->bind_param("iss", $user_id, $content, $image);

    if ($stmt->execute()) {
        return $conn->insert_id;
    } else {
        return false;
    }
}

/**
 * Delete user post
 * @param int $post_id Post ID
 * @return bool Success or failure
 */
function delete_user_post($post_id) {
    $conn = get_db_connection();

    if (!$conn) {
        return false;
    }

    $stmt = $conn->prepare("DELETE FROM user_posts WHERE id = ?");
    $stmt->bind_param("i", $post_id);

    return $stmt->execute();
}

/**
 * Update user profile image
 * @param int $user_id User ID
 * @param array $file Uploaded file data
 * @return array|bool Success or failure with message
 */
function update_profile_image($user_id, $file) {
    // Include upload functions
    require_once 'upload-functions.php';

    // Upload the image with automatic resizing to 200x200
    $upload_result = upload_image(
        $file,
        'uploads/profile_images',
        ['image/jpeg', 'image/png', 'image/gif'],
        5242880,
        true, // Enable resizing
        200,  // Max width
        200   // Max height
    );

    if (isset($upload_result['error'])) {
        return ['error' => $upload_result['error']];
    }

    // Get the image path
    $image_path = $upload_result['path'];

    // Connect to database
    $conn = get_db_connection();

    if (!$conn) {
        return ['error' => 'Database connection failed'];
    }

    // Update the user's profile image
    $stmt = $conn->prepare("UPDATE users SET profile_image = ? WHERE id = ?");
    $stmt->bind_param("si", $image_path, $user_id);

    $result = $stmt->execute();
    $stmt->close();
    $conn->close();

    if ($result) {
        return ['success' => 'Profile image updated successfully', 'path' => $image_path];
    } else {
        return ['error' => 'Failed to update profile image in database'];
    }
}

/**
 * Update user banner image
 * @param int $user_id User ID
 * @param array $file Uploaded file data
 * @return array|bool Success or failure with message
 */
function update_banner_image($user_id, $file) {
    // Include upload functions
    require_once 'upload-functions.php';

    // Upload the image with resizing to fit banner dimensions
    $upload_result = upload_image(
        $file,
        'uploads/banner_images',
        ['image/jpeg', 'image/png', 'image/gif'],
        5242880,
        true,  // Enable resizing
        1200,  // Max width
        400    // Max height
    );

    if (isset($upload_result['error'])) {
        return ['error' => $upload_result['error']];
    }

    // Get the image path
    $image_path = $upload_result['path'];

    // Connect to database
    $conn = get_db_connection();

    if (!$conn) {
        return ['error' => 'Database connection failed'];
    }

    // Update the user's banner image
    $stmt = $conn->prepare("UPDATE users SET banner_image = ? WHERE id = ?");
    $stmt->bind_param("si", $image_path, $user_id);

    $result = $stmt->execute();
    $stmt->close();
    $conn->close();

    if ($result) {
        return ['success' => 'Banner image updated successfully', 'path' => $image_path];
    } else {
        return ['error' => 'Failed to update banner image in database'];
    }
}

/**
 * Add donation
 * @param int $donor_id Donor user ID (optional for anonymous)
 * @param int $recipient_id Recipient user ID
 * @param float $amount Donation amount
 * @param string $message Donation message (optional)
 * @param bool $is_anonymous Whether donation is anonymous
 * @param string $transaction_id Payment transaction ID
 * @return int|bool New donation ID or false on failure
 */
function add_donation($recipient_id, $amount, $message = '', $is_anonymous = false, $donor_id = null, $transaction_id = '') {
    $conn = get_db_connection();

    if (!$conn) {
        return false;
    }

    if ($donor_id) {
        $stmt = $conn->prepare("INSERT INTO donations (donor_id, recipient_id, amount, message, is_anonymous, transaction_id) VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->bind_param("iidsss", $donor_id, $recipient_id, $amount, $message, $is_anonymous, $transaction_id);
    } else {
        $stmt = $conn->prepare("INSERT INTO donations (recipient_id, amount, message, is_anonymous, transaction_id) VALUES (?, ?, ?, ?, ?)");
        $stmt->bind_param("idsss", $recipient_id, $amount, $message, $is_anonymous, $transaction_id);
    }

    if ($stmt->execute()) {
        return $conn->insert_id;
    } else {
        return false;
    }
}

/**
 * Get user donations (received)
 * @param int $user_id User ID
 * @param int $limit Number of donations to return (0 for all)
 * @return array Donations
 */
function get_user_donations($user_id, $limit = 0) {
    $conn = get_db_connection();

    if (!$conn) {
        return [];
    }

    $sql = "SELECT d.id, d.donor_id, d.amount, d.message, d.is_anonymous, d.created_at,
            CASE WHEN d.is_anonymous = 1 THEN 'Anonymous' ELSE u.username END as donor_name
            FROM donations d
            LEFT JOIN users u ON d.donor_id = u.id
            WHERE d.recipient_id = ?
            ORDER BY d.created_at DESC";

    if ($limit > 0) {
        $sql .= " LIMIT ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ii", $user_id, $limit);
    } else {
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("i", $user_id);
    }

    $stmt->execute();
    $result = $stmt->get_result();

    $donations = [];
    while ($row = $result->fetch_assoc()) {
        $donations[] = $row;
    }

    return $donations;
}

/**
 * Generate QR code for user profile
 * @param string $slug User slug
 * @return string QR code image URL
 */
function generate_profile_qr($slug) {
    // Base URL for profiles - use the clean URL structure
    $base_url = "https://" . $_SERVER['HTTP_HOST'] . "/profile/" . $slug;

    // Google Chart API for QR code
    $qr_url = "https://chart.googleapis.com/chart?cht=qr&chs=300x300&chl=" . urlencode($base_url);

    return $qr_url;
}
?>
