
# 20 Tips for Instructors Making Online Learning Courses Accessible

#youtube #lectures #accessibility #vocation 

---
Author:: <PERSON><PERSON>, Ph.D
Date:: 10/3/2017
Key:: Public
Transcription:: April Cyr
Link:: [20 Tips for Instructors about Making Online Learning Courses Accessible - YouTube](https://www.youtube.com/watch?v=_KBhUORLB20)
Related::  [MHFA Feedback](https://docs.google.com/document/d/1SNbQn_a-ts8j8dX1JH5ZnzubyBtATM1ER4jMH2BZtzo/edit?usp=sharing),  [[aachips/public/blog/assets/portfolio/Markdown Lecture]] 

---
## Universal Design in Online Learning

![[Pasted image 20230127211130.png]]

>>Narrator: <PERSON><PERSON> shares historical highlights and tips to create accessible online learning activities.
>>
>> <PERSON><PERSON>: I’m going to share with you a little about online learning access and what instructional designers and faculty members need to know. Often whabout online learning access andg about this topic, faculty members often say, "I don’t have enough time," and then, if it’s not that, "I don’t have enough funding." That gets some support. "And I don’t have enough technical support for me."
>> 
>> Well, what I can come back with, politely, of course, is there are some things that we can all do. We don’t need to do them all at once. We can do them incrementally but make our courses accessible.
>> 
>> That led to this publication entitled 20 Tips For Teaching An Accessible Online Course. And so I’m going to go through that a little bit. It’s part of our AccessCyberlearning project, one of our resources for other projects. And I’ll just step back for a minute, back to 1995.
>> 
>> I taught the first online course at the University of Washington. That’s kind of a little-known fact about me. It was in 1995, and I’m still shocked that they hired an 18-year-old girl to offer these courses!
>> 
>> [Audience laughs]
>> 
>> But I was pretty precocious, so I offered the class with Dr. Norm Coombs at the Rochester Institute of Technology. He and I had been talking about accessible technology around the country, and the University of Washington had a pervasive distance learning program, all based on the postal mail system.
>> 
>> And they mailed out things, including videos, to students. And they proctored their exams in proctoring centers around the country, so it was pretty elaborate what we had here. And I wanted to ensure my somewhat hidden agenda was that these online courses would be accessible to individuals with disabilities. Still, I was curious if you could deliver a class online close to what you could do onsite.
>> 
>> Particularly something like assistive technology, which usuall people touch things and manipulate things. So I recruited Norm Coombs to teach this class with me, Adaptive Technology for People With Disabilities. Back then, for those old enough to remember, people communicated using email, and we had email-based discussion lists. We had a Gopher server - Gopher from the University of Minnesota, of course. It was an online catalog system, all text-based.
>> 
>> We’d get into a little outline of your resources, which would link to help around the country. And we received an award for having the most comprehensive Gopher server for people with disabilities in the world. And I don’t know if we had any competition. But anyway, that was our course library.
>> 
>> Then we used Telnet, which allowed us to log onto NASA and other extensive computing systems. The students had to learn a different language to correspond with these systems with each because they developed their interface. And then we used File Transfer Protocol to move files around, to get images, whatever, that we wanted to move around. 
>> 
>> So it was basic technology. All the materials that we had we put in text format. You had to. It was on Gopher. So we did that. We did use postal mail. We mailed out publications; we mailed out videos. DO-IT was around. We had already made some DO-IT videos on VHS tapes, and they were captioned, and audio described, believe it or not. And we mailed those out to the participants in the class. We got the course together. I gave them Norm Coombs’ resume, and they approved him as an instructor here at the University of Washington.
>> 
>> And then it sort of came out in the meeting one time when we were talking about proctoring exams. I said, "Well, we really can’t do proctoring exams in these different locations because people write those out longhand, and Norm Coombs is blind, so he won’t be able to read those if they’re not in electronic form, and I’ll have to grade all those myself, and I’m not interested in having to do all that work. Or we must hire somebody to go over to RIT and read to him."
>> 
>> Now I have to say that the program people were not amused by the fact that I kind of dropped this idea about Norm Coombs being blind. It was a little humorous, and I didn't think it had anything to do with them accepting him as an instructor, even back in those days.
>> 
>> And so they allowed us to go forward with this. We offered this course to be fully accessible, and the distance learning program was kind of the end of the first time we showed it. She said, "Well, tell me, Sheryl, after all this work, how many people with disabilities even took this course? So how do you even know if it's successful?"
>> 
>> I am proud to say we have no idea how many people with disabilities took this course. Because we just designed it to be fully accessible. No one had to disclose. So they weren't amused, but we continued teaching that class. But I'm happy to say our first class was fully accessible. 
>> 
>> In applying universal design to online learning, we provide multiple ways to gain, interact, and demonstrate knowledge. We have this publication that we've created on 20 tips for teaching an online course that is fully accessible to people with disabilities.
>> 
>> Nine tips are about webpages and documents, images, and videos; the other 11 are instructional methods.
>> 
>> And when I'm working with faculty who are reluctant to admit that they might be able to adopt some accessible technology practices, I ask them to take the challenge of selecting a few of these to make their courses accessible. And it points out how the faculty need to work with the technology people and the designers in developing their online systems. I'm going through these fairly quickly.
>>
>> But to give you an idea of what we tell faculty members and designers to look for in online learning.
  
## 1) Clear, Consistent Layouts, & organization schemes
Providing clear and consistent layouts and organizational schemes. That is something that every instructor should do to present their material.

## 2) Structured Headings
Of course, those layouts should be apparent to someone who is blind. We structure the headings to ensure that someone can access them using screen readers and see the organization of the content rather than just dumping a bunch of text that would have to be read from the beginning to the end.

## 3) Descriptive Wording for Hyperlinks
So this faculty member would also use descriptive wording for hyperlinks. Someone using a screen reader might want to tab through and go to each one of the web resources on a page so they could see where they want to start or if they wish to go to those resources. And so if you use the wording on each of your underlined text, "click here," that person will be able to read all those with no problem, but what they will read is "click here, click here, click here, click here."

In contrast, if you provide underlined descriptive wording, that link might say "DO-IT website." Then, that person would know what they're going to be linking to and can decide then if that's where they want to be.

A very simple thing.
This doesn't take any more time than putting the "click here" there.
But it makes it accessible to people using screen readers.

## 4) PDFs avoided; no scanned image PDFs.
PDFs are tricky. We can make them accessible but you have to ask yourself, why again was I creating that PDF? Sometimes you're forced to do it because it's a PDF that's out on the internet. But if you're making a lesson or even your syllabus in your online class, do you want to include that as a PDF file or do you want to cut and paste the content right into the learning management system itself into that window so it is text and then use the features within Canvas or what other system you're using to structure the headings, so you've made it accessible that way. That's what I do.

## 5) Alt Text Descriptions of Content in Images
The text descriptions of the content when images are provided. Whenever an image is presented, you just describe that text. And some learning management systems prompt you to do that. So you're reminded, but even if it doesn't, you can put that in. So, people will say, "Well, but it's just a little logo here. It doesn't mean anything. Why do I have to have the text description?" Well, the person who's blind and trying to access your course doesn't know that that image doesn't include anything significant. For our DO-IT website, we have on our logo on our website we have "DO-IT logo" as an alternate text for that image. We should describe what it looks like. Other people would say it doesn't matter what it looks like but a person who is blind must know that it's a logo that they do or don’t need to pay attention to.

## 6) Using large bold fonts on uncluttered pages with plain backgrounds

[This is a slightly outdated practice. It is still a good practice, but there are many ways someone can be visually impaired or blind, and sometimes that impairment can favor near-sightedness or far-sightedness. Depending on the impairment, producing large bold fonts on uncluttered pages can make navigating and reading challenging. Collaborating with your vision-impaired students on what is needed over that semester or course session is essential. Having a few different formats and file types of information presented, as well as course materials, is helpful as well and makes the Instructor's life much easier. -April]

With the PowerPoints we’re using, we're assuming that their vision is such that it's difficult for them to see the content, so we automatically provide large bold fonts on uncluttered pages with plain backgrounds.

## 7) High Color Contrasts
You can figure this out on your own. Sometimes you go to a website, and it's light green on dark green. It's like, what did people think about that? And to avoid the problematic ones for the colorblind, such as red and green. There are resources on the web that you can test some of these things, so it's easy to find. Content and navigation are accessible using the keyboard alone. 

## 8) Content and navigation are accessible using the keyboard alone
Sometimes there's only little you can do about that. If it's the product you're using, that's the problem. But if you have things you control, you need to be aware of that. It's important to remember that and to work on that continually. So it's good to know that.

## 9) Captioning and Audio Transcription
Make sure that the videos are captioned, and the audio described. Captioning first. Audio described is essential, too, but as I said, if you're creating your video, you can often make it so that it's fairly accessible for people who are blind right from the beginning. That is going to take some technical support. You can read the content on our Accessible IT website, but you might have somebody help you.

## 10) Accommodate for a wide range of tech skills 
Ensure that your course is designed for a wide range of technical skills. This is another thing that doesn't take a rocket scientist to figure out how to do, but so often, we’re used to using the technology that we’re using, and we don't tell students how to use it. We must remember that even if you have some tech-savvy students in your course, they might never have used that product. This might be the first Canvas class that they've taken. So providing a little overview of the technology you're using to deliver that course and where they can get help, just include that in the syllabus or early instruction in a lesson or two.
  
## 11) Content Presented in Multiple Ways
Make sure the content is presented in multiple ways, so if you're using a video in the class, make sure it's captioned. A transcript is excellent, but I recommend you provide a different version because we present content differently when we're writing what is shown in a video. So many of our videos have a handout connected with them online. It's not a handout but a publication with that content. But it's written in the way you usually would write that content. And so just because you have a video doesn't mean you shouldn't do that other; that can be within your lesson in a class or a separate document. So that's providing that content in multiple ways.

## 12) Acronyms and jargon. Spell them out & define them, or don't use them
[Dr. Burgstahler skipped over a description and explanation for this tip. So here is my commentary. Having a Glossary Index can be tremendously helpful in improving the accessibility of the learning materials. ]

## 13) Make Instructions and expectations clear
Make sure that they're apparent. Sometimes putting content in the syllabus that, years ago, I would've given later. I’d think perhaps this assignment’s only going to take a week, so I'll give it in the middle of the class. Well, maybe it will take somebody longer than that, so provide them with the assignment at the beginning. They shouldn't be penalized for actually working ahead. Even if they can't do all parts of it, they can at least think about it in terms of what you’re teaching. 

## 14) Examples and assignments relevant to a diverse audience
Use a rubric or use other techniques to make sure they know what they're supposed to be able to produce. Make sure that examples and assignments are relevant to a diverse audience. And so just sit back and think about it. You don't have to survey your students. You can think of a variety of people that are accessing that class. It might be an older student. You've got male and female students. People from many different disciplines take your course, whatever it happens to be, so try to have a few examples of a concept that might appeal to a diverse audience.

## 15) Outlines and other scaffolding tools provided
Make sure that outlines and other scaffolding tools are provided so that's what would apply to online learning. 
  
## 16) Adequate Opportunities for Practice
Be sure to provide adequate practice opportunities so in an online class, sometimes I would have something required like required reading, and then if I think some people might want to have more instruction or it might be just a little diversion of what we're talking about, then I put in all caps the name of that lesson and in parentheses "optional" and so that is a cue for someone who wants to do a little bit more, feels like they need more practice or a little more information. But I only require that some of the students do that. 

People have different levels of knowledge coming into your course but also different learning styles, and it might take them longer or a shorter period to learn something. Still, it also people require, requires more practice than others.

## 17) Provide adequate time for activities, projects, and tests.

A lot of this can be solved by putting it in the syllabus. I've asked the program managers if I can open my class a week before it starts. It makes it more difficult for the instructor. Well, that's my problem. And so I don't want to discourage people from moving forward. They may be swamped in a couple of weeks. So they want to get ahead, but I make it clear what discussion we’re on and when we're discussing specific topics, so they have to stay with the class in that regard. And I always send out a notice to the whole class saying, "I opened this class a week early so you people can get started. If you haven't started, you’re not behind. We're starting today." And so I don't let the class get away from me, even if I’m letting some students work ahead.

## 18) Feedback on parts and corrective opportunities provided

Providing feedback on parts of an assignment and corrective opportunities so if you're assigning a big project for a class, at least invite students to give you a draft of what they're going to do, and you can provide feedback on it or part of it say you're open to that or you can build it into the assignments and say everyone has to turn in an outline or whatever by such and such a date. So the students don't have that experience where they finish the whole project, and then you look at it and say, "Oh, they didn't understand what I was asking for."

## 19) Options for communicating and collaborating

## 20) Options for Demonstrated Learning

For demonstrating learning, you can give students options for displaying learning. Give them opportunities for what project they want to do, or another way to demonstrate understanding throughout the course.So you want projects, and you want to have short answer tests, and you want to have true and false and multiple choice and whatever it is, or students creating videos or whatever you have in the class. Just ensure there's a variety so that if someone needs to improve at one of those things, they can still do well in the class.

### Conclusion

So that's just a simple overview of what you can do in an online course to make it more accessible to students with disabilities. Not too tricky and not too technical. And what I challenge faculty members to do then, particularly the ones that say, "Well, I just don't have time to do this," is to look through here and circle a few things, circle a few numbers of things that they can do like right away. And no one has trouble finding them, but even if you just did a few of these things, given you aren't doing them already, it would make a better class.

![[Pasted image 20230127221630.png]]
