<?php
/**
 * Database configuration file
 * 
 * This is a placeholder. In a real implementation, you would
 * include your database connection details here.
 */

// Database credentials
define('DB_HOST', 'localhost');
define('DB_NAME', 'dogfarts_db');
define('DB_USER', 'db_user');
define('DB_PASS', 'db_password');

/**
 * Connect to the database
 * 
 * @return PDO Database connection
 */
function getDbConnection() {
    try {
        $dsn = 'mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4';
        $options = [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ];
        
        return new PDO($dsn, DB_USER, DB_PASS, $options);
    } catch (PDOException $e) {
        // In a production environment, you would log this error
        // and show a generic message to the user
        die('Database connection failed: ' . $e->getMessage());
    }
}
