<?php
/**
 * Testimonial Management Functions
 * Functions for handling user testimonials including creation, moderation, and display
 */

require_once 'db.php';
require_once 'functions.php';

/**
 * Create a new testimonial
 * @param array $data Testimonial data
 * @return array Result with success/error status
 */
function create_testimonial($data) {
    $conn = get_db_connection();
    
    if (!$conn) {
        return ['error' => 'Database connection failed'];
    }
    
    // Validate required fields
    $required_fields = ['subject_user_id', 'author_name', 'author_email', 'relationship_type', 'testimonial_content'];
    foreach ($required_fields as $field) {
        if (empty($data[$field])) {
            return ['error' => "Field '$field' is required"];
        }
    }
    
    // Validate email format
    if (!filter_var($data['author_email'], FILTER_VALIDATE_EMAIL)) {
        return ['error' => 'Invalid email format'];
    }
    
    // Validate user exists
    $user_check = $conn->prepare("SELECT id FROM users WHERE id = ?");
    $user_check->bind_param("i", $data['subject_user_id']);
    $user_check->execute();
    if ($user_check->get_result()->num_rows === 0) {
        return ['error' => 'User not found'];
    }
    
    // Check if user allows testimonials
    $settings = get_user_testimonial_settings($data['subject_user_id']);
    if (!$settings['allow_testimonials']) {
        return ['error' => 'This user is not accepting testimonials'];
    }
    
    // Prepare SQL statement
    $sql = "INSERT INTO user_testimonials (
        subject_user_id, author_name, author_email, author_organization,
        relationship_type, relationship_description, testimonial_content,
        work_arrangement_rating, reliability_rating, communication_rating, overall_rating,
        best_practices, challenges, recommendations, is_anonymous
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $stmt = $conn->prepare($sql);

    // Prepare variables for binding (bind_param requires variable references)
    $subject_user_id = $data['subject_user_id'];
    $author_name = $data['author_name'];
    $author_email = $data['author_email'];
    $author_organization = $data['author_organization'] ?? null;
    $relationship_type = $data['relationship_type'];
    $relationship_description = $data['relationship_description'] ?? null;
    $testimonial_content = $data['testimonial_content'];
    $work_arrangement_rating = $data['work_arrangement_rating'] ?? null;
    $reliability_rating = $data['reliability_rating'] ?? null;
    $communication_rating = $data['communication_rating'] ?? null;
    $overall_rating = $data['overall_rating'] ?? null;
    $best_practices = $data['best_practices'] ?? null;
    $challenges = $data['challenges'] ?? null;
    $recommendations = $data['recommendations'] ?? null;
    $is_anonymous = $data['is_anonymous'] ?? 0;

    // Bind parameters
    $stmt->bind_param("issssssiiiisssi",
        $subject_user_id,
        $author_name,
        $author_email,
        $author_organization,
        $relationship_type,
        $relationship_description,
        $testimonial_content,
        $work_arrangement_rating,
        $reliability_rating,
        $communication_rating,
        $overall_rating,
        $best_practices,
        $challenges,
        $recommendations,
        $is_anonymous
    );
    
    if ($stmt->execute()) {
        $testimonial_id = $conn->insert_id;
        
        // Send notification to user if enabled
        send_testimonial_notification($data['subject_user_id'], 'new_testimonial', $testimonial_id);
        
        return ['success' => true, 'testimonial_id' => $testimonial_id];
    } else {
        return ['error' => 'Failed to create testimonial: ' . $conn->error];
    }
}

/**
 * Get testimonials for a user
 * @param int $user_id User ID
 * @param string $status Filter by moderation status (optional)
 * @param int $limit Number of testimonials to return (optional)
 * @return array Array of testimonials
 */
function get_user_testimonials($user_id, $status = 'approved', $limit = null) {
    $conn = get_db_connection();

    if (!$conn) {
        return [];
    }

    // Check if testimonials table exists
    $table_check = $conn->query("SHOW TABLES LIKE 'user_testimonials'");
    if (!$table_check || $table_check->num_rows === 0) {
        // Table doesn't exist, return empty array
        return [];
    }

    $sql = "SELECT t.*,
            CASE WHEN t.is_anonymous = 1 THEN 'Anonymous' ELSE t.author_name END as display_name,
            CASE WHEN t.is_anonymous = 1 THEN NULL ELSE t.author_organization END as display_organization
            FROM user_testimonials t
            WHERE t.subject_user_id = ?";

    $params = [$user_id];
    $types = "i";

    if ($status) {
        $sql .= " AND t.moderation_status = ?";
        $params[] = $status;
        $types .= "s";
    }

    $sql .= " ORDER BY t.is_featured DESC, t.created_at DESC";

    if ($limit) {
        $sql .= " LIMIT ?";
        $params[] = $limit;
        $types .= "i";
    }

    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        // SQL prepare failed, return empty array
        return [];
    }

    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();

    $testimonials = [];
    while ($row = $result->fetch_assoc()) {
        $testimonials[] = $row;
    }

    return $testimonials;
}

/**
 * Get testimonials pending moderation
 * @param int $limit Number of testimonials to return (optional)
 * @return array Array of pending testimonials
 */
function get_pending_testimonials($limit = null) {
    $conn = get_db_connection();
    
    if (!$conn) {
        return [];
    }
    
    $sql = "SELECT t.*, u.username, u.slug 
            FROM user_testimonials t 
            JOIN users u ON t.subject_user_id = u.id 
            WHERE t.moderation_status = 'pending' 
            ORDER BY t.created_at ASC";
    
    if ($limit) {
        $sql .= " LIMIT ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("i", $limit);
    } else {
        $stmt = $conn->prepare($sql);
    }
    
    $stmt->execute();
    $result = $stmt->get_result();
    
    $testimonials = [];
    while ($row = $result->fetch_assoc()) {
        $testimonials[] = $row;
    }
    
    return $testimonials;
}

/**
 * Moderate a testimonial (approve/reject)
 * @param int $testimonial_id Testimonial ID
 * @param string $status New status ('approved' or 'rejected')
 * @param int $admin_id Admin user ID
 * @param string $notes Admin notes (optional)
 * @return array Result with success/error status
 */
function moderate_testimonial($testimonial_id, $status, $admin_id, $notes = '') {
    $conn = get_db_connection();
    
    if (!$conn) {
        return ['error' => 'Database connection failed'];
    }
    
    if (!in_array($status, ['approved', 'rejected', 'hidden'])) {
        return ['error' => 'Invalid status'];
    }
    
    // Get testimonial info for notification
    $testimonial_info = get_testimonial_by_id($testimonial_id);
    if (!$testimonial_info) {
        return ['error' => 'Testimonial not found'];
    }
    
    $sql = "UPDATE user_testimonials SET 
            moderation_status = ?, 
            moderation_notes = ?, 
            moderated_by = ?, 
            moderated_at = NOW() 
            WHERE id = ?";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ssii", $status, $notes, $admin_id, $testimonial_id);
    
    if ($stmt->execute()) {
        // Send notification to user
        send_testimonial_notification($testimonial_info['subject_user_id'], 'testimonial_' . $status, $testimonial_id);
        
        return ['success' => true];
    } else {
        return ['error' => 'Failed to update testimonial: ' . $conn->error];
    }
}

/**
 * Get a single testimonial by ID
 * @param int $testimonial_id Testimonial ID
 * @return array|false Testimonial data or false if not found
 */
function get_testimonial_by_id($testimonial_id) {
    $conn = get_db_connection();
    
    if (!$conn) {
        return false;
    }
    
    $stmt = $conn->prepare("SELECT * FROM user_testimonials WHERE id = ?");
    $stmt->bind_param("i", $testimonial_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    return $result->fetch_assoc();
}

/**
 * Get user testimonial settings
 * @param int $user_id User ID
 * @return array Settings array
 */
function get_user_testimonial_settings($user_id) {
    $conn = get_db_connection();

    if (!$conn) {
        return get_default_testimonial_settings();
    }

    // Check if testimonial_settings table exists
    $table_check = $conn->query("SHOW TABLES LIKE 'testimonial_settings'");
    if (!$table_check || $table_check->num_rows === 0) {
        // Table doesn't exist, return default settings
        return get_default_testimonial_settings();
    }

    $stmt = $conn->prepare("SELECT * FROM testimonial_settings WHERE user_id = ?");
    if (!$stmt) {
        return get_default_testimonial_settings();
    }

    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        return $result->fetch_assoc();
    } else {
        // Create default settings for user
        return create_default_testimonial_settings($user_id);
    }
}

/**
 * Get default testimonial settings
 * @return array Default settings
 */
function get_default_testimonial_settings() {
    return [
        'allow_testimonials' => true,
        'require_approval' => false,
        'show_ratings' => true,
        'show_author_info' => true,
        'email_notifications' => true,
        'auto_approve_known' => false
    ];
}

/**
 * Create default testimonial settings for a user
 * @param int $user_id User ID
 * @return array Settings array
 */
function create_default_testimonial_settings($user_id) {
    $conn = get_db_connection();

    if (!$conn) {
        return get_default_testimonial_settings();
    }

    // Check if testimonial_settings table exists
    $table_check = $conn->query("SHOW TABLES LIKE 'testimonial_settings'");
    if (!$table_check || $table_check->num_rows === 0) {
        // Table doesn't exist, return default settings
        return get_default_testimonial_settings();
    }

    $defaults = get_default_testimonial_settings();

    $sql = "INSERT INTO testimonial_settings (user_id, allow_testimonials, require_approval, show_ratings, show_author_info, email_notifications, auto_approve_known)
            VALUES (?, ?, ?, ?, ?, ?, ?)";

    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        return $defaults;
    }

    $stmt->bind_param("iiiiiii",
        $user_id,
        $defaults['allow_testimonials'],
        $defaults['require_approval'],
        $defaults['show_ratings'],
        $defaults['show_author_info'],
        $defaults['email_notifications'],
        $defaults['auto_approve_known']
    );

    if ($stmt->execute()) {
        return array_merge($defaults, ['user_id' => $user_id]);
    } else {
        return $defaults;
    }
}

/**
 * Update user testimonial settings
 * @param int $user_id User ID
 * @param array $settings Settings to update
 * @return bool Success status
 */
function update_testimonial_settings($user_id, $settings) {
    $conn = get_db_connection();
    
    if (!$conn) {
        return false;
    }
    
    // Ensure settings exist first
    get_user_testimonial_settings($user_id);
    
    $allowed_fields = ['allow_testimonials', 'require_approval', 'show_ratings', 'show_author_info', 'email_notifications', 'auto_approve_known'];
    
    $updates = [];
    $params = [];
    $types = "";
    
    foreach ($allowed_fields as $field) {
        if (isset($settings[$field])) {
            $updates[] = "$field = ?";
            $params[] = $settings[$field] ? 1 : 0;
            $types .= "i";
        }
    }
    
    if (empty($updates)) {
        return true; // Nothing to update
    }
    
    $sql = "UPDATE testimonial_settings SET " . implode(", ", $updates) . " WHERE user_id = ?";
    $params[] = $user_id;
    $types .= "i";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param($types, ...$params);
    
    return $stmt->execute();
}

/**
 * Send testimonial notification
 * @param int $user_id User ID to notify
 * @param string $type Notification type
 * @param int $testimonial_id Testimonial ID
 * @return bool Success status
 */
function send_testimonial_notification($user_id, $type, $testimonial_id) {
    // Include notification functions if not already loaded
    if (!function_exists('create_notification_record')) {
        require_once 'notification-functions.php';
    }

    // Call the notification function from notification-functions.php
    return send_testimonial_notification_internal($user_id, $type, $testimonial_id);
}

/**
 * Get testimonial statistics for a user
 * @param int $user_id User ID
 * @return array Statistics array
 */
function get_user_testimonial_stats($user_id) {
    $conn = get_db_connection();

    if (!$conn) {
        return [
            'total_count' => 0,
            'average_rating' => null,
            'approved_count' => 0,
            'pending_count' => 0,
            'latest_testimonial' => null
        ];
    }

    // Check if testimonials table exists
    $table_check = $conn->query("SHOW TABLES LIKE 'user_testimonials'");
    if (!$table_check || $table_check->num_rows === 0) {
        // Table doesn't exist, return default stats
        return [
            'total_count' => 0,
            'average_rating' => null,
            'approved_count' => 0,
            'pending_count' => 0,
            'latest_testimonial' => null
        ];
    }

    $sql = "SELECT
            COUNT(*) as total_count,
            AVG(overall_rating) as average_rating,
            COUNT(CASE WHEN moderation_status = 'approved' THEN 1 END) as approved_count,
            COUNT(CASE WHEN moderation_status = 'pending' THEN 1 END) as pending_count,
            MAX(created_at) as latest_testimonial
            FROM user_testimonials
            WHERE subject_user_id = ?";

    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        return [
            'total_count' => 0,
            'average_rating' => null,
            'approved_count' => 0,
            'pending_count' => 0,
            'latest_testimonial' => null
        ];
    }

    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();

    return $result->fetch_assoc();
}

/**
 * Get testimonial categories
 * @return array Array of categories
 */
function get_testimonial_categories() {
    $conn = get_db_connection();
    
    if (!$conn) {
        return [];
    }
    
    $stmt = $conn->prepare("SELECT * FROM testimonial_categories WHERE is_active = 1 ORDER BY display_order ASC");
    $stmt->execute();
    $result = $stmt->get_result();
    
    $categories = [];
    while ($row = $result->fetch_assoc()) {
        $categories[] = $row;
    }
    
    return $categories;
}

/**
 * Delete a testimonial
 * @param int $testimonial_id Testimonial ID
 * @param int $admin_id Admin user ID (for logging)
 * @return array Result with success/error status
 */
function delete_testimonial($testimonial_id, $admin_id) {
    $conn = get_db_connection();
    
    if (!$conn) {
        return ['error' => 'Database connection failed'];
    }
    
    $stmt = $conn->prepare("DELETE FROM user_testimonials WHERE id = ?");
    $stmt->bind_param("i", $testimonial_id);
    
    if ($stmt->execute()) {
        return ['success' => true];
    } else {
        return ['error' => 'Failed to delete testimonial: ' . $conn->error];
    }
}
?>
