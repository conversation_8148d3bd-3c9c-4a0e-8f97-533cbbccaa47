<?php
/**
 * Blog Posts Display
 *
 * This script fetches and displays blog posts from the database,
 * sorted by date with the newest posts first.
 */

// Include necessary files
require_once '../config/config.php';
require_once '../config/db.php';
require_once '../lib/functions.php';
include_once '../includes/header.php';
include_once '../includes/nav.php';

// Get database connection
$db = getDbConnection();

// Initialize variables
$posts = [];
$error = '';

try {
    // Fetch posts with prepared statement
    $stmt = $db->prepare("
        SELECT
            p.id,
            p.title,
            p.content,
            p.date_published,
            p.author_id,
            a.name as author_name
        FROM
            posts p
        LEFT JOIN
            authors a ON p.author_id = a.id
        WHERE
            p.status = 'published'
        ORDER BY
            p.date_published DESC
    ");

    $stmt->execute();
    $posts = $stmt->fetchAll();

    // For each post, fetch its categories
    foreach ($posts as $key => $post) {
        $stmt = $db->prepare("
            SELECT
                c.id,
                c.name,
                c.slug
            FROM
                categories c
            JOIN
                post_categories pc ON c.id = pc.category_id
            WHERE
                pc.post_id = :post_id
            ORDER BY
                c.name ASC
        ");

        $stmt->bindParam(':post_id', $post['id'], PDO::PARAM_INT);
        $stmt->execute();
        $posts[$key]['categories'] = $stmt->fetchAll();
    }
} catch (PDOException $e) {
    // Log the error (in a production environment)
    error_log('Database query error: ' . $e->getMessage());

    // Set error message
    $error = 'Sorry, we could not retrieve the blog posts at this time.';
}
?>

<main class="blog-main">
    <h1>Dogfarts School Blog</h1>

    <?php if ($error): ?>
        <div class="error-message">
            <?php echo htmlspecialchars($error); ?>
        </div>
    <?php elseif (empty($posts)): ?>
        <div class="no-posts">
            <p>No blog posts found. Please check back later.</p>
        </div>
    <?php else: ?>
        <div class="posts-container">
            <?php foreach ($posts as $post): ?>
                <article class="post">
                    <h2 class="post-title">
                        <a href="post.php?id=<?php echo $post['id']; ?>">
                            <?php echo htmlspecialchars($post['title']); ?>
                        </a>
                    </h2>

                    <div class="post-meta">
                        <span class="post-date">
                            <?php echo formatDate($post['date_published']); ?>
                        </span>

                        <?php if (!empty($post['author_name'])): ?>
                            <span class="post-author">
                                by <?php echo htmlspecialchars($post['author_name']); ?>
                            </span>
                        <?php endif; ?>

                        <?php if (!empty($post['categories'])): ?>
                            <span class="post-categories">
                                in
                                <?php
                                $categoryLinks = [];
                                foreach ($post['categories'] as $category) {
                                    $categoryLinks[] = '<a href="category.php?slug=' .
                                        htmlspecialchars($category['slug']) . '">' .
                                        htmlspecialchars($category['name']) . '</a>';
                                }
                                echo implode(', ', $categoryLinks);
                                ?>
                            </span>
                        <?php endif; ?>
                    </div>

                    <div class="post-excerpt">
                        <?php
                        // Create an excerpt from the content
                        echo createExcerpt($post['content']);
                        ?>
                        <a href="post.php?id=<?php echo $post['id']; ?>" class="read-more">
                            Read More
                        </a>
                    </div>
                </article>
            <?php endforeach; ?>
        </div>

        <!-- Pagination could be added here if needed -->

    <?php endif; ?>
</main>

<?php
include_once 'sidebar.php';
include_once '../includes/footer.php';
?>
