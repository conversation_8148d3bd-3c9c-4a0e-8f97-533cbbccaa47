<?php
/**
 * Simple Order Form Test
 * 
 * This is a minimal version of the order form for testing basic functionality
 */

// Include configuration
require_once 'orders/config.php';

$message = '';
$success = false;

if ($_POST && isset($_POST['submit_test'])) {
    // Simple test data
    $test_data = [
        'order_id' => 'TEST_' . uniqid(),
        'timestamp' => date('Y-m-d H:i:s'),
        'name' => $_POST['name'] ?? 'Test User',
        'email' => $_POST['email'] ?? '<EMAIL>',
        'phone' => '************',
        'address' => '123 Test St',
        'city' => 'Test City',
        'state' => 'TS',
        'zip' => '12345',
        'bags' => 1,
        'payment' => 'PayPal',
        'message' => $_POST['message'] ?? 'Test order',
        'instructions' => 'Test instructions'
    ];
    
    $message .= "<h3>Testing Order Processing...</h3>";
    
    // Test CSV save
    $csv_file = ORDERS_DIRECTORY . ORDERS_CSV_FILE;
    $message .= "<p><strong>CSV File:</strong> " . $csv_file . "</p>";
    
    try {
        $csv_success = saveToSpreadsheet($test_data, $csv_file);
        $message .= "<p style='color: " . ($csv_success ? 'green' : 'red') . "'>CSV Save: " . ($csv_success ? 'SUCCESS' : 'FAILED') . "</p>";
    } catch (Exception $e) {
        $message .= "<p style='color: red'>CSV Save Error: " . $e->getMessage() . "</p>";
    }
    
    // Test Google Sheets
    try {
        $sheets_success = saveToGoogleSheet($test_data);
        $message .= "<p style='color: " . ($sheets_success ? 'green' : 'red') . "'>Google Sheets: " . ($sheets_success ? 'SUCCESS' : 'FAILED') . "</p>";
    } catch (Exception $e) {
        $message .= "<p style='color: red'>Google Sheets Error: " . $e->getMessage() . "</p>";
    }
    
    // Test emails
    try {
        $admin_email_success = sendEmailNotification($test_data, ADMIN_EMAIL);
        $message .= "<p style='color: " . ($admin_email_success ? 'green' : 'red') . "'>Admin Email: " . ($admin_email_success ? 'SUCCESS' : 'FAILED') . "</p>";
    } catch (Exception $e) {
        $message .= "<p style='color: red'>Admin Email Error: " . $e->getMessage() . "</p>";
    }
    
    try {
        $customer_email_success = sendCustomerConfirmation($test_data);
        $message .= "<p style='color: " . ($customer_email_success ? 'green' : 'red') . "'>Customer Email: " . ($customer_email_success ? 'SUCCESS' : 'FAILED') . "</p>";
    } catch (Exception $e) {
        $message .= "<p style='color: red'>Customer Email Error: " . $e->getMessage() . "</p>";
    }
    
    $success = ($csv_success || $sheets_success);
    
    if ($success) {
        $message .= "<p style='color: green; font-weight: bold;'>✓ Order processing completed successfully!</p>";
    } else {
        $message .= "<p style='color: red; font-weight: bold;'>✗ Order processing failed!</p>";
    }
}

// Include the functions from the main order file
function saveToGoogleSheet($data) {
    try {
        $postData = [
            'order_data' => json_encode($data),
            'token' => GOOGLE_APP_TOKEN
        ];

        $options = [
            'http' => [
                'header'  => "Content-type: application/x-www-form-urlencoded\r\n",
                'method'  => 'POST',
                'content' => http_build_query($postData),
                'timeout' => 30
            ],
        ];

        $context = stream_context_create($options);
        $result = file_get_contents(GOOGLE_WEBAPP_URL, false, $context);

        if ($result !== false) {
            $response = json_decode($result, true);
            if ($response && isset($response['success'])) {
                return $response['success'];
            } else {
                return stripos($result, 'success') !== false;
            }
        }

        return false;
    } catch (Exception $e) {
        error_log("Google Sheets error: " . $e->getMessage());
        return false;
    }
}

function saveToSpreadsheet($data, $filename) {
    try {
        $directory = dirname($filename);
        if (!is_dir($directory)) {
            mkdir($directory, 0755, true);
        }
        
        $file_exists = file_exists($filename);
        $file = fopen($filename, 'a');

        if (!$file) {
            return false;
        }

        if (!$file_exists) {
            $headers = ['Order ID', 'Timestamp', 'Name', 'Email', 'Phone', 'Address', 'City', 'State', 'Zip', 'Bags', 'Payment Method', 'Message', 'Special Instructions'];
            fputcsv($file, $headers);
        }

        $row = [
            $data['order_id'],
            $data['timestamp'],
            $data['name'],
            $data['email'],
            $data['phone'],
            $data['address'],
            $data['city'],
            $data['state'],
            $data['zip'],
            $data['bags'],
            $data['payment'],
            $data['message'],
            $data['instructions']
        ];

        $result = fputcsv($file, $row);
        fclose($file);

        return $result !== false;
    } catch (Exception $e) {
        error_log("CSV save error: " . $e->getMessage());
        return false;
    }
}

function sendEmailNotification($data, $admin_email) {
    $subject = 'Test Order - ' . $data['order_id'];
    $message = "Test order notification\n\nOrder ID: " . $data['order_id'] . "\nName: " . $data['name'] . "\nEmail: " . $data['email'];
    $headers = "From: " . FROM_EMAIL . "\r\n";
    return mail($admin_email, $subject, $message, $headers);
}

function sendCustomerConfirmation($data) {
    $subject = 'Test Order Confirmation - ' . $data['order_id'];
    $message = "Test order confirmation\n\nThank you for your test order!\n\nOrder ID: " . $data['order_id'];
    $headers = "From: " . FROM_EMAIL . "\r\n";
    return mail($data['email'], $subject, $message, $headers);
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Order Form Test</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, textarea { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #1C5D3B; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0f3d26; }
        .message { background: #f9f9f9; padding: 15px; border-radius: 4px; margin: 20px 0; }
    </style>
</head>
<body>
    <h1>Simple Order Form Test</h1>
    
    <p>This is a simplified test version of the order form to help diagnose issues.</p>
    
    <?php if ($message): ?>
        <div class="message">
            <?php echo $message; ?>
        </div>
    <?php endif; ?>
    
    <form method="post">
        <div class="form-group">
            <label for="name">Name:</label>
            <input type="text" id="name" name="name" value="Test User" required>
        </div>
        
        <div class="form-group">
            <label for="email">Email:</label>
            <input type="email" id="email" name="email" value="<EMAIL>" required>
        </div>
        
        <div class="form-group">
            <label for="message">Message:</label>
            <textarea id="message" name="message" rows="3">This is a test order to verify functionality.</textarea>
        </div>
        
        <button type="submit" name="submit_test">Submit Test Order</button>
    </form>
    
    <h2>Configuration Info</h2>
    <ul>
        <li><strong>Admin Email:</strong> <?php echo ADMIN_EMAIL; ?></li>
        <li><strong>From Email:</strong> <?php echo FROM_EMAIL; ?></li>
        <li><strong>Orders Directory:</strong> <?php echo ORDERS_DIRECTORY; ?></li>
        <li><strong>CSV File:</strong> <?php echo ORDERS_DIRECTORY . ORDERS_CSV_FILE; ?></li>
        <li><strong>Google Webapp URL:</strong> <?php echo substr(GOOGLE_WEBAPP_URL, 0, 50) . '...'; ?></li>
    </ul>
    
    <h2>Quick Links</h2>
    <ul>
        <li><a href="debug_test.php">Run Debug Test</a></li>
        <li><a href="email_test.php">Run Email Test</a></li>
        <li><a href="order.php">Full Order Form</a></li>
    </ul>
</body>
</html>
