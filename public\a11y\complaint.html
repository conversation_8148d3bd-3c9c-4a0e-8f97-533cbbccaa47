
<!DOCTYPE html>
<html lang="en-US">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta name='robots' content='noindex, follow' />

	<!-- This site is optimized with the Yoast SEO plugin v23.1 - https://yoast.com/wordpress/plugins/seo/ -->
	<title>Virtual Care Access - Apple Chip Blog</title>
	<meta name="description" content="Telehealth and online platforms made crucial support readily available, empowering individuals, especially those from vulnerable communities, to prioritize their well-being. However, a worrying trend is taking hold: Managed Care Organizations (MCOs) are de-funding and ending virtual service access, both for workers and clients." />
	<link rel="canonical" href="https://aachips.co/virtual-care-access/" />
	<meta property="og:locale" content="en_US" />
	<meta property="og:type" content="article" />
	<meta property="og:title" content="Virtual Care Access - Apple Chip Blog" />
	<meta property="og:description" content="Telehealth and online platforms made crucial support readily available, empowering individuals, especially those from vulnerable communities, to prioritize their well-being. However, a worrying trend is taking hold: Managed Care Organizations (MCOs) are de-funding and ending virtual service access, both for workers and clients." />
	<meta property="og:url" content="https://aachips.co/virtual-care-access/" />
	<meta property="og:site_name" content="Apple Chip Blog" />
	<meta property="article:modified_time" content="2024-05-22T02:43:29+00:00" />
	<meta name="twitter:card" content="summary_large_image" />
	<meta name="twitter:label1" content="Est. reading time" />
	<meta name="twitter:data1" content="2 minutes" />
	<script type="application/ld+json" class="yoast-schema-graph">{"@context":"https://schema.org","@graph":[{"@type":"WebPage","@id":"https://aachips.co/virtual-care-access/","url":"https://aachips.co/virtual-care-access/","name":"Virtual Care Access - Apple Chip Blog","isPartOf":{"@id":"/#website"},"datePublished":"2024-02-06T16:49:51+00:00","dateModified":"2024-05-22T02:43:29+00:00","description":"Telehealth and online platforms made crucial support readily available, empowering individuals, especially those from vulnerable communities, to prioritize their well-being. However, a worrying trend is taking hold: Managed Care Organizations (MCOs) are de-funding and ending virtual service access, both for workers and clients.","breadcrumb":{"@id":"https://aachips.co/virtual-care-access/#breadcrumb"},"inLanguage":"en-US","potentialAction":[{"@type":"ReadAction","target":["https://aachips.co/virtual-care-access/"]}]},{"@type":"BreadcrumbList","@id":"https://aachips.co/virtual-care-access/#breadcrumb","itemListElement":[{"@type":"ListItem","position":1,"name":"Home","item":"https://aachips.co/"},{"@type":"ListItem","position":2,"name":"Virtual Care Access"}]},{"@type":"WebSite","@id":"/#website","url":"/","name":"Apple Chip Blog","description":"Cool snacks. Cool facts.","potentialAction":[{"@type":"SearchAction","target":{"@type":"EntryPoint","urlTemplate":"/?s={search_term_string}"},"query-input":"required name=search_term_string"}],"inLanguage":"en-US"}]}</script>
	<!-- / Yoast SEO plugin. -->


<link rel='dns-prefetch' href='//cdn.chatway.app' />
<link rel='dns-prefetch' href='//fonts.googleapis.com' />
<script>
window._wpemojiSettings = {"baseUrl":"https:\/\/s.w.org\/images\/core\/emoji\/15.0.3\/72x72\/","ext":".png","svgUrl":"https:\/\/s.w.org\/images\/core\/emoji\/15.0.3\/svg\/","svgExt":".svg","source":{"concatemoji":"https:\/\/aachips.co\/wp-includes\/js\/wp-emoji-release.min.js?ver=6.5.5"}};
/*! This file is auto-generated */
!function(i,n){var o,s,e;function c(e){try{var t={supportTests:e,timestamp:(new Date).valueOf()};sessionStorage.setItem(o,JSON.stringify(t))}catch(e){}}function p(e,t,n){e.clearRect(0,0,e.canvas.width,e.canvas.height),e.fillText(t,0,0);var t=new Uint32Array(e.getImageData(0,0,e.canvas.width,e.canvas.height).data),r=(e.clearRect(0,0,e.canvas.width,e.canvas.height),e.fillText(n,0,0),new Uint32Array(e.getImageData(0,0,e.canvas.width,e.canvas.height).data));return t.every(function(e,t){return e===r[t]})}function u(e,t,n){switch(t){case"flag":return n(e,"\ud83c\udff3\ufe0f\u200d\u26a7\ufe0f","\ud83c\udff3\ufe0f\u200b\u26a7\ufe0f")?!1:!n(e,"\ud83c\uddfa\ud83c\uddf3","\ud83c\uddfa\u200b\ud83c\uddf3")&&!n(e,"\ud83c\udff4\udb40\udc67\udb40\udc62\udb40\udc65\udb40\udc6e\udb40\udc67\udb40\udc7f","\ud83c\udff4\u200b\udb40\udc67\u200b\udb40\udc62\u200b\udb40\udc65\u200b\udb40\udc6e\u200b\udb40\udc67\u200b\udb40\udc7f");case"emoji":return!n(e,"\ud83d\udc26\u200d\u2b1b","\ud83d\udc26\u200b\u2b1b")}return!1}function f(e,t,n){var r="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope?new OffscreenCanvas(300,150):i.createElement("canvas"),a=r.getContext("2d",{willReadFrequently:!0}),o=(a.textBaseline="top",a.font="600 32px Arial",{});return e.forEach(function(e){o[e]=t(a,e,n)}),o}function t(e){var t=i.createElement("script");t.src=e,t.defer=!0,i.head.appendChild(t)}"undefined"!=typeof Promise&&(o="wpEmojiSettingsSupports",s=["flag","emoji"],n.supports={everything:!0,everythingExceptFlag:!0},e=new Promise(function(e){i.addEventListener("DOMContentLoaded",e,{once:!0})}),new Promise(function(t){var n=function(){try{var e=JSON.parse(sessionStorage.getItem(o));if("object"==typeof e&&"number"==typeof e.timestamp&&(new Date).valueOf()<e.timestamp+604800&&"object"==typeof e.supportTests)return e.supportTests}catch(e){}return null}();if(!n){if("undefined"!=typeof Worker&&"undefined"!=typeof OffscreenCanvas&&"undefined"!=typeof URL&&URL.createObjectURL&&"undefined"!=typeof Blob)try{var e="postMessage("+f.toString()+"("+[JSON.stringify(s),u.toString(),p.toString()].join(",")+"));",r=new Blob([e],{type:"text/javascript"}),a=new Worker(URL.createObjectURL(r),{name:"wpTestEmojiSupports"});return void(a.onmessage=function(e){c(n=e.data),a.terminate(),t(n)})}catch(e){}c(n=f(s,u,p))}t(n)}).then(function(e){for(var t in e)n.supports[t]=e[t],n.supports.everything=n.supports.everything&&n.supports[t],"flag"!==t&&(n.supports.everythingExceptFlag=n.supports.everythingExceptFlag&&n.supports[t]);n.supports.everythingExceptFlag=n.supports.everythingExceptFlag&&!n.supports.flag,n.DOMReady=!1,n.readyCallback=function(){n.DOMReady=!0}}).then(function(){return e}).then(function(){var e;n.supports.everything||(n.readyCallback(),(e=n.source||{}).concatemoji?t(e.concatemoji):e.wpemoji&&e.twemoji&&(t(e.twemoji),t(e.wpemoji)))}))}((window,document),window._wpemojiSettings);
</script>
<link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin><style id='wp-emoji-styles-inline-css'>

	img.wp-smiley, img.emoji {
		display: inline !important;
		border: none !important;
		box-shadow: none !important;
		height: 1em !important;
		width: 1em !important;
		margin: 0 0.07em !important;
		vertical-align: -0.1em !important;
		background: none !important;
		padding: 0 !important;
	}
</style>
<style id='pdfemb-pdf-embedder-viewer-style-inline-css'>
.wp-block-pdfemb-pdf-embedder-viewer{max-width:none}

</style>
<style id='classic-theme-styles-inline-css'>
/*! This file is auto-generated */
.wp-block-button__link{color:#fff;background-color:#32373c;border-radius:9999px;box-shadow:none;text-decoration:none;padding:calc(.667em + 2px) calc(1.333em + 2px);font-size:1.125em}.wp-block-file__button{background:#32373c;color:#fff;text-decoration:none}
</style>
<link rel='stylesheet' id='trp-floater-language-switcher-style-css' href='https://aachips.co/wp-content/plugins/translatepress-multilingual/assets/css/trp-floater-language-switcher.css?ver=2.8.1' media='all' />
<link rel='stylesheet' id='trp-language-switcher-style-css' href='https://aachips.co/wp-content/plugins/translatepress-multilingual/assets/css/trp-language-switcher.css?ver=2.8.1' media='all' />
<link rel='stylesheet' id='bricks-frontend-css' href='https://aachips.co/wp-content/themes/bricks/assets/css/frontend.min.css?ver=1716603503' media='all' />
<style id='bricks-frontend-inline-css'>
@font-face{font-family:"Salsa";font-weight:500;font-style:italic;font-display:swap;src:url(https://aachips.co/wp-content/uploads/Salsa-Regular.ttf) format("truetype");}

</style>
<link rel='stylesheet' id='bricks-child-css' href='https://aachips.co/wp-content/themes/bricks-child/style.css?ver=1708397722' media='all' />
<style id='bricks-frontend-inline-inline-css'>
html {scroll-behavior: smooth}
/* GLOBAL VARIABLES CSS */
:root {}
/* COLOR VARS */
:root {
--bricks-color-b9a385: #f5f5f5;
--bricks-color-6cd52b: #e0e0e0;
--bricks-color-206941: #9e9e9e;
--bricks-color-c608b7: #616161;
--bricks-color-72ece0: #424242;
--bricks-color-16e4f4: #212121;
--bricks-color-3b8cdd: #ffeb3b;
--bricks-color-6f6c26: #ffc107;
--bricks-color-55aa51: #ff9800;
--bricks-color-fa7a1f: #ff5722;
--bricks-color-58bf73: #f44336;
--bricks-color-8e2276: #9c27b0;
--bricks-color-e48cc0: #2196f3;
--bricks-color-e42f7a: #03a9f4;
--bricks-color-191ebe: #81D4FA;
--bricks-color-8f1ede: #4caf50;
--bricks-color-3a95a7: #8bc34a;
--bricks-color-01ada1: #cddc39;
--bricks-color-kyuuwl: #c33748;
}

/* THEME STYLE CSS */

/* BREAKPOINT: Desktop (BASE) */
:root .bricks-color-primary {color: var(--bricks-color-kyuuwl)}
:root .bricks-background-primary {background-color: var(--bricks-color-kyuuwl)}
:root .bricks-color-secondary {color: #1c5d3b}
:root .bricks-background-secondary {background-color: #1c5d3b}
:root .bricks-color-light {color: #ffffff}
:root .bricks-background-light {background-color: #ffffff}
:root .bricks-color-dark {color: var(--bricks-color-16e4f4)}
:root .bricks-background-dark {background-color: var(--bricks-color-16e4f4)}
:root * {border-color: var(--bricks-color-16e4f4)}
 body {font-size: 16px; font-family: "ABeeZee"; color: var(--bricks-color-16e4f4)}
 h1 {font-family: "Salsa"; font-weight: 500}
 h2 {font-family: "Salsa"; font-weight: 500}
 h4 {color: var(--bricks-color-kyuuwl)}
 h5 {font-family: "Salsa"; font-weight: 500}
.brxe-divider .line {border-top-width: 1rem; border-top-color: var(--bricks-color-kyuuwl)}
.brxe-divider .icon i {color: var(--bricks-color-kyuuwl)}
 .bricks-button {color: var(--bricks-color-b9a385); padding-top: 1rem; padding-right: 1rem; padding-bottom: 1rem; padding-left: 1rem; border: .5px groove var(--bricks-color-b9a385); border-radius: 8px}
.brxe-post-meta .item {padding-right: auto; padding-left: auto}
 :where(.brxe-accordion .accordion-content-wrapper) a,  :where(.brxe-icon-box .content) a,  :where(.brxe-list) a,  :where(.brxe-post-content) a:not(.bricks-button),  :where(.brxe-posts .dynamic p) a,  :where(.brxe-shortcode) a,  :where(.brxe-tabs .tab-content) a,  :where(.brxe-team-members) .description a,  :where(.brxe-testimonials) .testimonial-content-wrapper a,  :where(.brxe-text) a,  :where(a.brxe-text),  :where(.brxe-text-basic) a,  :where(a.brxe-text-basic),  :where(.brxe-post-comments) .comment-content a {color: var(--bricks-color-8e2276); font-style: italic; text-decoration: underline; font-weight: 500}
.brxe-image .icon {background-color: var(--bricks-color-b9a385)}
.brxe-related-posts .post-content {padding-top: 1rem; padding-right: 1rem; padding-bottom: 1rem; padding-left: 1rem}
.brxe-post-title {font-family: "Salsa"; font-weight: 500; color: var(--bricks-color-kyuuwl)}
.brxe-sidebar {color: #000000; text-align: center; text-shadow: 1px 1px 1px var(--bricks-color-kyuuwl)}

/* PAGE CSS (ID: 745, 184, 144) */

/* BREAKPOINT: Desktop (BASE) */
        :root {
  --color-primary: hsl(var(--hue-primary), var(--saturation-primary), var(--lightness-primary));
  --color-secondary: hsl(var(--hue-secondary), var(--saturation-secondary), var(--lightness-secondary));
  --color-background: hsl(var(--hue-background), var(--saturation-background), var(--lightness-background));
}

*,
::before,
::after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Roboto', sans-serif;
  line-height: 1.6;
  font-size: 1rem;
  color: var(--color-text);
  background-color: var(--color-background);
  scroll-behavior: smooth;
}

h1, h2, h3, h4, h5, h6 {
  line-height: 1.2;
}

a {
  text-decoration: none;
  color: inherit;
}

button, input, select, optgroup, textarea {
  appearance: none;
  outline: none;
  user-select: none;
  resize: vertical;
  -webkit-appearance: none;
}

input, textarea, select {
  display: block;
  width: 100%;
  border: solid 1px var(--color-border);
  background-color: var(--color-white);
  padding: .5em;
}

input:focus, textarea:focus, select:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 1px 1px var(--color-primary);
}

button {
  cursor: pointer;
  border: none;
  padding: .5em calc(1em + .5ch);
  background-color: var(--color-primary);
  color: var(--color-background);
  transition: transform .2s ease-out;
}

button:hover {
  background-color: var(--color-secondary);
  transform: translateY(-2px);
}

button:active {
  transform: translateY(2px);
}

button:disabled {
  opacity: .5;
  cursor: default;
}

/* Layout */
.grid-two-cols {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 2rem;
  max-width: 1200px;
  margin: auto;
  padding: 2rem 1rem;
}

.grid-two-cols > div {
  flex: 1 1 minmax(0, 50%);
}

.grid-four-cols {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 2rem;
  max-width: 1200px;
  margin: auto;
  padding: 2rem 1rem;
}

.grid-four-cols > article {
  flex: 1 1 minmax(0, 25%);
  text-align: center;
}

.card {
  background-color: var(--color-white);
  border-radius: 5px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.card h3 {
  margin-bottom: .5rem;
}

.card p {
  margin-bottom: 1rem;
}

.social-links li {
  margin-right: 1rem;
}

.social-links i {
  font-size: 1.2rem;
  transition: transform .2s ease-out;
}

.social-links i:hover {
  transform: scale(1.2);
}

.modal-trigger {
  display: inline-block;
}

/* Modal window */
.modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0);
  visibility: hidden;
  z-index: -1;
  background-color: var(--color-white);
  border-radius: 5px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
  padding: 2rem;
  max-width: 80%;
  width: 500px;
  animation: slide-down .5s cubic-bezier(0.77, 0, 0.175, 1) forwards;
}

@keyframes slide-down {
  0% {transform: translate(-50%, -50%) scale(0); visibility: visible;}
  80% {transform: translate(-50%, -50%) rotateX(-10deg);}
  90% {transform: translate(-50%, -50%) rotateX(0);}
  100% {transform: translate(-50%, -50%) scale(1); visibility: visible;}
}

.modal__close {
  position: absolute;
  top: 1rem;
  right: 1rem;
  cursor: pointer;
  border: none;
  background: transparent;
  font-size: 2rem;
}

.modal:target {z-index: 1000;}

.modal:target ~ .overlay {display: block;}

.overlay {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: none;
}

/* Hero section */
.hero {
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: scroll;
  background-size: cover;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  color: white;
  position: relative;
}

.hero::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('hero-bg.jpg');
  filter: brightness(70%);
}

.hero img {
  width: 150px;
  height: 150px;
  object-fit: cover;
  border-radius: 50%;
  margin-bottom: 2rem;
}

.hero h1 {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.hero h2 {
  font-size: 1.8rem;
  font-weight: normal;
}

.hero p {
  font-size: 1.2rem;
  margin-top: 2rem;
  max-width: 600px;
}

/* Utility classes */
.container {
  max-width: 1200px;
  margin: auto;
  padding: 2rem 1rem;
}

.grid-two-cols {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 2rem;
  max-width: 1200px;
  margin: auto;
  padding: 2rem 1rem;
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1rem;
  gap: 0.5rem;
  border-radius: 0.5rem;
  font-size: 1rem;
  line-height: 1;
  text-align: center;
  text-decoration: none;
  touch-action: manipulation;
  cursor: pointer;
  background-color: var(--color-primary);
  color: var(--color-white);
  transition: filter 0.15s ease-out;
}

.btn:not(:disabled):hover {
  filter: contrast(115%);
}

.btn:focus {
  box-shadow: 0 0 0 1px black;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn:not(:disabled).btn--loading {
  pointer-events: none;
}

.btn--icon-left svg {
  width: 1.25rem;
  height: 1.25rem;
}

.btn--icon-right svg {
  width: 1.25rem;
  height: 1.25rem;
  fill: currentColor;
  opacity: 0.5;
}

.btn--icon-right:hover svg {
  opacity: 0.75;
}

@media screen and (min-width: 768px) {
  body {font-size: 1.1rem;}
}

@media screen and (min-width: 1024px) {
  .hero h1 {font-size: 4rem;}
}


/* HEADER CSS (ID: 184) */

/* BREAKPOINT: Desktop (BASE) */
#brxe-1373f2 {scroll-snap-align: center}
#brxe-1373f2:not(.tag) {object-fit: contain}
#brxe-1373f2 img {object-fit: contain}

/* BREAKPOINT: Mobile portrait */
@media (max-width: 478px) {
#brxe-1373f2:not(.tag) {object-fit: contain}
#brxe-1373f2 img {object-fit: contain}
#brxe-1373f2 {transform:  scaleX(1.25) scaleY(1.25)}
}

/* BREAKPOINT: Desktop (BASE) */
#brxe-e9e0a6 {width: 300px}

/* BREAKPOINT: Mobile landscape */
@media (max-width: 767px) {
#brxe-e9e0a6 {width: auto}
}
/* BREAKPOINT: Mobile portrait */
@media (max-width: 478px) {
#brxe-e9e0a6 {width: 250px}
}
@media (max-width: 767px) {
#brxe-9fedce .bricks-nav-menu-wrapper { display: none; }
#brxe-9fedce .bricks-mobile-menu-toggle { display: block; }
}
/* BREAKPOINT: Desktop (BASE) */
#brxe-9fedce {scroll-snap-align: center; font-weight: 700}

/* BREAKPOINT: Tablet portrait */
@media (max-width: 991px) {
/* CUSTOM CSS */
#brxe-9fedce li {
  white-space: nowrap;
}
}
/* BREAKPOINT: Mobile portrait */
@media (max-width: 478px) {
#brxe-9fedce {transform:  translateX(-25px) scaleX(1.5) scaleY(1.5)}
}

/* BREAKPOINT: Desktop (BASE) */
#brxe-c0a245 {width: auto; justify-content: center; align-items: center}

/* BREAKPOINT: Mobile landscape */
@media (max-width: 767px) {
#brxe-c0a245 {align-items: flex-end}
}

/* BREAKPOINT: Desktop (BASE) */
#brxe-6331c8 li.has-link a, #brxe-6331c8 li.no-link {padding-top: 15px; padding-right: 15px; padding-bottom: 15px; padding-left: 15px; align-items: center}
#brxe-6331c8 {color: #ffffff}
#brxe-6331c8 .repeater-item:nth-child(1) svg {height: 35px}

/* BREAKPOINT: Mobile portrait */
@media (max-width: 478px) {
#brxe-6331c8 li {transform:  translateX(-80px)}
}

/* BREAKPOINT: Desktop (BASE) */
#brxe-fca855 {width: auto}

/* BREAKPOINT: Mobile landscape */
@media (max-width: 767px) {
#brxe-fca855 {grid-column: 1/3; align-items: center}
}
/* BREAKPOINT: Mobile portrait */
@media (max-width: 478px) {
#brxe-fca855 {align-items: center}
}

/* BREAKPOINT: Desktop (BASE) */
#brxe-874c0c {flex-direction: row; color: var(--bricks-color-kyuuwl); font-family: "Salsa"; font-weight: 500; min-height: 104px; display: grid; align-items: initial; align-items: center; justify-content: space-between; grid-template-columns: auto 1fr auto}

/* BREAKPOINT: Mobile landscape */
@media (max-width: 767px) {
#brxe-874c0c {justify-content: space-evenly; grid-template-columns: 1fr 1fr}
}
/* BREAKPOINT: Mobile portrait */
@media (max-width: 478px) {
#brxe-874c0c {justify-content: space-evenly; grid-template-columns: 280px 1fr}
}

/* BREAKPOINT: Desktop (BASE) */
.brxe-fvuexe {font-weight: 800; background-color: #fffaed}

/* BREAKPOINT: Mobile landscape */
@media (max-width: 767px) {
.brxe-fvuexe {padding-right: 2rem; padding-left: 2rem}
}

/* CONTENT CSS (ID: 745) */


/* BREAKPOINT: Desktop (BASE) */
#brxe-sdxuyf {margin-right: auto; margin-left: auto}


/* BREAKPOINT: Desktop (BASE) */
#brxe-jzmbvy {width: 50%; margin-right: auto; margin-left: auto}


/* FOOTER CSS (ID: 144) */

/* BREAKPOINT: Desktop (BASE) */
#brxe-bb8b39 {color: var(--bricks-color-kyuuwl); font-size: 27px; line-height: 38px; font-weight: 500; font-family: "Salsa"}

/* BREAKPOINT: Mobile portrait */
@media (max-width: 478px) {
#brxe-bb8b39 {align-self: center}
}

/* BREAKPOINT: Desktop (BASE) */
#brxe-8df127 {color: var(--bricks-color-kyuuwl); font-size: 15px; line-height: 30px; font-family: "Salsa"; font-weight: 500}
#brxe-8df127  a {color: var(--bricks-color-kyuuwl); font-size: 15px; line-height: 30px; font-family: "Salsa"; font-weight: 500; font-size: inherit}

/* BREAKPOINT: Mobile portrait */
@media (max-width: 478px) {
#brxe-8df127 {align-self: center; text-align: center}
#brxe-8df127  a {text-align: center; font-size: inherit}
}

/* BREAKPOINT: Desktop (BASE) */
#brxe-f220b3 .form-group:nth-child(1) {width: 65%}
#brxe-f220b3 .form-group:nth-child(2) {width: 65%}
#brxe-f220b3 {align-self: center; margin-top: 25px; margin-bottom: 10px}
#brxe-f220b3 .form-group input {color: #212121; line-height: 43px; background-color: #ffffff; border-radius: 30px 0 0 30px; padding-right: 15px; padding-left: 15px}
#brxe-f220b3 select {color: #212121; line-height: 43px; background-color: #ffffff; border-radius: 30px 0 0 30px; padding-right: 15px; padding-left: 15px; color: #8a8a8a}
#brxe-f220b3 textarea {color: #212121; line-height: 43px; background-color: #ffffff; border-radius: 30px 0 0 30px; padding-right: 15px; padding-left: 15px}
#brxe-f220b3 .flatpickr {background-color: #ffffff; border-radius: 30px 0 0 30px; padding-right: 15px; padding-left: 15px}
#brxe-f220b3 .bricks-button:not([type=submit]) {border-radius: 30px 0 0 30px}
#brxe-f220b3 .choose-files {border-radius: 30px 0 0 30px}
#brxe-f220b3 .submit-button-wrapper {width: 35%}
#brxe-f220b3 button[type=submit].bricks-button {border-radius: 0 30px 30px 0}
#brxe-f220b3 .form-group:not(:last-child) {padding-top: 0; padding-right: 0; padding-bottom: 0; padding-left: 0}
#brxe-f220b3 ::placeholder {color: #8a8a8a}
#brxe-f220b3 .bricks-button {line-height: 30px}

/* BREAKPOINT: Mobile portrait */
@media (max-width: 478px) {
#brxe-f220b3 {align-self: center}
}

/* BREAKPOINT: Desktop (BASE) */
#brxe-c82c01 {color: var(--bricks-color-kyuuwl); font-size: 14px; font-family: "Salsa"; font-weight: 500}
#brxe-c82c01  a {color: var(--bricks-color-kyuuwl); font-size: 14px; font-family: "Salsa"; font-weight: 500; font-size: inherit}

/* BREAKPOINT: Mobile portrait */
@media (max-width: 478px) {
#brxe-c82c01 {align-self: center}
}

/* BREAKPOINT: Desktop (BASE) */
#brxe-008f66 {padding-right: 50px}

/* BREAKPOINT: Mobile portrait */
@media (max-width: 478px) {
#brxe-008f66 {padding-right: 0}
}

/* BREAKPOINT: Desktop (BASE) */
#brxe-7ed787 {color: var(--bricks-color-kyuuwl); font-size: 27px; line-height: 38px; font-weight: 500; font-family: "Salsa"}

/* BREAKPOINT: Mobile portrait */
@media (max-width: 478px) {
#brxe-7ed787 {margin-top: 25px; align-self: center}
}

/* BREAKPOINT: Desktop (BASE) */
#brxe-qlfuak {background-color: var(--bricks-color-b9a385); color: var(--bricks-color-kyuuwl); font-family: "Salsa"; font-weight: 500}
#brxe-qlfuak  a {color: var(--bricks-color-kyuuwl); font-family: "Salsa"; font-weight: 500; font-size: inherit}


/* BREAKPOINT: Desktop (BASE) */
#brxe-625689 {color: #ffffff; font-size: 15px; line-height: 30px}
#brxe-625689  a {color: #ffffff; font-size: 15px; line-height: 30px; font-size: inherit}

/* BREAKPOINT: Mobile portrait */
@media (max-width: 478px) {
#brxe-625689 {align-self: center; text-align: center}
#brxe-625689  a {text-align: center; font-size: inherit}
}

/* BREAKPOINT: Desktop (BASE) */
#brxe-lewqfb {color: var(--bricks-color-kyuuwl); font-family: "Salsa"; font-weight: 500}


/* BREAKPOINT: Desktop (BASE) */
#brxe-beff6d {padding-right: 50px; color: var(--bricks-color-kyuuwl)}

/* BREAKPOINT: Mobile portrait */
@media (max-width: 478px) {
#brxe-beff6d {padding-right: 0}
}

/* BREAKPOINT: Desktop (BASE) */
#brxe-52277c {color: var(--bricks-color-kyuuwl); font-size: 27px; line-height: 38px; font-weight: 500; font-family: "Salsa"}

/* BREAKPOINT: Mobile portrait */
@media (max-width: 478px) {
#brxe-52277c {align-self: center; margin-top: 25px}
}

/* BREAKPOINT: Desktop (BASE) */
#brxe-zykzqz {background-color: var(--bricks-color-b9a385); color: var(--bricks-color-kyuuwl); font-family: "Salsa"; font-weight: 500}
#brxe-zykzqz  a {color: var(--bricks-color-kyuuwl); font-family: "Salsa"; font-weight: 500; font-size: inherit}


/* BREAKPOINT: Desktop (BASE) */
#brxe-2a5920 li.has-link a, #brxe-2a5920 li.no-link {padding-top: 15px; padding-right: 15px; padding-bottom: 15px; padding-left: 15px; background-color: var(--bricks-color-b9a385)}
#brxe-2a5920 {color: #ffffff; line-height: 1; font-size: 25px; bottom: 20px; position: relative}
#brxe-2a5920 .repeater-item:nth-child(1) {background-color: var(--bricks-color-6cd52b)}
#brxe-2a5920 li {margin-top: 18px}

/* BREAKPOINT: Mobile portrait */
@media (max-width: 478px) {
#brxe-2a5920 {align-self: center}
}

/* BREAKPOINT: Desktop (BASE) */
#brxe-fb6f9e {flex-direction: row; padding-right: 20px; padding-left: 20px; background-color: var(--bricks-color-b9a385); color: var(--bricks-color-kyuuwl)}


/* BREAKPOINT: Desktop (BASE) */
#brxe-3c7195 {align-self: stretch !important; width: 100%; align-items: center; background-color: var(--bricks-color-b9a385)}


</style>
<link rel='stylesheet' id='bricks-google-fonts-css' href='https://fonts.googleapis.com/css2?family=ABeeZee:ital,wght@0,400;1,400&#038;display=swap&#038;family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&#038;display=swap&#038;family=Salsa:wght@400;500&#038;display=swap' media='all' />
<script id="asenha-public-js-extra">
var phpVars = {"externalPermalinksEnabled":"1"};
</script>
<script src="https://aachips.co/wp-content/plugins/admin-site-enhancements/assets/js/external-permalinks.js?ver=7.1.5" id="asenha-public-js"></script>
<meta name="generator" content="WordPress 6.5.5" />
<link rel='shortlink' href='https://aachips.co/?p=745' />
<link rel="alternate" type="application/json+oembed" href="https://aachips.co/wp-json/oembed/1.0/embed?url=https%3A%2F%2Faachips.co%2Fvirtual-care-access%2F" />
<link rel="alternate" type="text/xml+oembed" href="https://aachips.co/wp-json/oembed/1.0/embed?url=https%3A%2F%2Faachips.co%2Fvirtual-care-access%2F&#038;format=xml" />
		<script type="text/javascript">
				(function(c,l,a,r,i,t,y){
					c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};t=l.createElement(r);t.async=1;
					t.src="https://www.clarity.ms/tag/"+i+"?ref=wordpress";y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
				})(window, document, "clarity", "script", "l6wg07j3fq");
		</script>
		<link rel="alternate" hreflang="en-US" href="https://aachips.co/virtual-care-access/"/>
<link rel="alternate" hreflang="en" href="https://aachips.co/virtual-care-access/"/>
		<script>
			document.documentElement.className = document.documentElement.className.replace('no-js', 'js');
		</script>
				<style>
			.no-js img.lazyload {
				display: none;
			}

			figure.wp-block-image img.lazyloading {
				min-width: 150px;
			}

						.lazyload, .lazyloading {
				opacity: 0;
			}

			.lazyloaded {
				opacity: 1;
				transition: opacity 400ms;
				transition-delay: 0ms;
			}

					</style>
		  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width,initial-scale=1">
  <title>Virtual Access Initiative - Improving Remote Healthcare Solutions</title>
  <meta name="description" content="Advocate for expanded remote healthcare solutions with our Virtual Access Initiative. Ensure equitable access for rural poor, disabled, single parents, and domestic violence victims.">
  <meta name="keywords" content="virtual access, remote healthcare, rural healthcare, disability healthcare, single parents healthcare, domestic violence healthcare, telemedicine, health equity">

  <!-- Schema Markup for Rich Snippet Integration -->
  <script type="application/ld+json">
  {
    "@context": "http://schema.org",
    "@type": "WebSite",
    "url": "https://www.yourdomain.com/virtual-access-initiative",
    "potentialAction": {
      "@type": "SearchAction",
      "target": "{search_endpoint}",
      "query-input": "required name=q"
    }
  }
  </script>

  <!-- Open Graph Protocol for Social Media Integration -->
  <meta property="og:locale" content="en_US">
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://www.yourdomain.com/virtual-access-initiative">
  <meta property="og:site_name" content="Virtual Access Initiative">
  <meta property="og:title" content="Virtual Access Initiative - Improving Remote Healthcare Solutions">
  <meta property="og:description" content="Advocate for expanded remote healthcare solutions with our Virtual Access Initiative. Ensure equitable access for rural poor, disabled, single parents, and domestic violence victims.">
  <meta property="og:image" content="https://www.yourdomain.com/images/virtual-access-thumbnail.jpg">

  <!-- Twitter Card Integration -->
  <meta name="twitter:card" content="summary">
  <meta name="twitter:site" content="@yourhandle">
  <meta name="twitter:creator" content="@yourhandle">
  <meta name="twitter:title" content="Virtual Access Initiative - Improving Remote Healthcare Solutions">
  <meta name="twitter:description" content="Advocate for expanded remote healthcare solutions with our Virtual Access Initiative. Ensure equitable access for rural poor, disabled, single parents, and domestic violence victims.">
  <meta name="twitter:image" content="https://www.yourdomain.com/images/virtual-access-thumbnail.jpg">

<link rel="icon" href="https://aachips.co/wp-content/uploads/2023/12/cropped-logo524x-32x32.png" sizes="32x32" />
<link rel="icon" href="https://aachips.co/wp-content/uploads/2023/12/cropped-logo524x-192x192.png" sizes="192x192" />
<link rel="apple-touch-icon" href="https://aachips.co/wp-content/uploads/2023/12/cropped-logo524x-180x180.png" />
<meta name="msapplication-TileImage" content="https://aachips.co/wp-content/uploads/2023/12/cropped-logo524x-270x270.png" />

<!-- Facebook Open Graph (by Bricks) -->
<meta property="og:url" content="https://aachips.co/virtual-care-access/" />
<meta property="og:site_name" content="Apple Chip Blog" />
<meta property="og:title" content="Virtual Care Access" />
<meta property="og:type" content="website" />
<style id="wpforms-css-vars-root">
				:root {
					--wpforms-field-border-radius: 3px;
--wpforms-field-border-style: solid;
--wpforms-field-border-size: 1px;
--wpforms-field-background-color: #ffffff;
--wpforms-field-border-color: rgba( 0, 0, 0, 0.25 );
--wpforms-field-border-color-spare: rgba( 0, 0, 0, 0.25 );
--wpforms-field-text-color: rgba( 0, 0, 0, 0.7 );
--wpforms-field-menu-color: #ffffff;
--wpforms-label-color: rgba( 0, 0, 0, 0.85 );
--wpforms-label-sublabel-color: rgba( 0, 0, 0, 0.55 );
--wpforms-label-error-color: #d63637;
--wpforms-button-border-radius: 3px;
--wpforms-button-border-style: none;
--wpforms-button-border-size: 1px;
--wpforms-button-background-color: #066aab;
--wpforms-button-border-color: #066aab;
--wpforms-button-text-color: #ffffff;
--wpforms-page-break-color: #066aab;
--wpforms-background-image: none;
--wpforms-background-position: center center;
--wpforms-background-repeat: no-repeat;
--wpforms-background-size: cover;
--wpforms-background-width: 100px;
--wpforms-background-height: 100px;
--wpforms-background-color: rgba( 0, 0, 0, 0 );
--wpforms-background-url: none;
--wpforms-container-padding: 0px;
--wpforms-container-border-style: none;
--wpforms-container-border-width: 1px;
--wpforms-container-border-color: #000000;
--wpforms-container-border-radius: 3px;
--wpforms-field-size-input-height: 43px;
--wpforms-field-size-input-spacing: 15px;
--wpforms-field-size-font-size: 16px;
--wpforms-field-size-line-height: 19px;
--wpforms-field-size-padding-h: 14px;
--wpforms-field-size-checkbox-size: 16px;
--wpforms-field-size-sublabel-spacing: 5px;
--wpforms-field-size-icon-size: 1;
--wpforms-label-size-font-size: 16px;
--wpforms-label-size-line-height: 19px;
--wpforms-label-size-sublabel-font-size: 14px;
--wpforms-label-size-sublabel-line-height: 17px;
--wpforms-button-size-font-size: 17px;
--wpforms-button-size-height: 41px;
--wpforms-button-size-padding-h: 15px;
--wpforms-button-size-margin-top: 10px;
--wpforms-container-shadow-size-box-shadow: none;

				}
			</style></head>

<body class="page-template-default page page-id-745 translatepress-en_US brx-body bricks-is-frontend wp-embed-responsive">		<a class="skip-link" href="#brx-content" aria-label="Skip to main content">Skip to main content</a>

					<a class="skip-link" href="#brx-footer" aria-label="Skip to footer">Skip to footer</a>
			<header id="brx-header"><section class="brxe-fvuexe brxe-section bricks-lazy-hidden"><div id="brxe-874c0c" class="brxe-container brx-grid bricks-lazy-hidden"><div id="brxe-e9e0a6" class="brxe-block bricks-lazy-hidden"><a id="brxe-1373f2" class="brxe-image tag" href="https://aachips.co/welcome2/"><img width="360" height="100" src="data:image/svg+xml,%3Csvg%20xmlns=&#039;http://www.w3.org/2000/svg&#039;%20viewBox=&#039;0%200%20360%20100&#039;%3E%3C/svg%3E" class="css-filter size-full bricks-lazy-hidden" alt="" decoding="async" data-src="https://aachips.co/wp-content/uploads/AppleChipBlog.png" data-type="string" /></a></div><div id="brxe-c0a245" class="brxe-block bricks-lazy-hidden"><div id="brxe-9fedce" data-script-id="9fedce" class="brxe-nav-menu"><nav class="bricks-nav-menu-wrapper mobile_landscape"><ul id="menu-navmain" class="bricks-nav-menu"><li id="menu-item-2123" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-2123 bricks-menu-item"><a href="https://aachips.co/single/">Posts</a></li>
<li id="menu-item-2148" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-2148 bricks-menu-item"><a href="https://aachips.co/about/">About</a></li>
<li id="menu-item-2125" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-2125 bricks-menu-item"><a href="https://aachips.co/booking/">Meet</a></li>
<li id="menu-item-2127" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-2127 bricks-menu-item"><a href="https://aachips.co/support/">Support</a></li>
<li id="menu-item-2128" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-2128 bricks-menu-item"><a href="https://aachips.co/newsletter/">News</a></li>
</ul></nav>			<button class="bricks-mobile-menu-toggle" aria-haspopup="true" aria-label="Mobile menu" aria-expanded="false">
				<span class="bar-top"></span>
				<span class="bar-center"></span>
				<span class="bar-bottom"></span>
			</button>
			<nav class="bricks-mobile-menu-wrapper left"><ul id="menu-navmain-1" class="bricks-mobile-menu"><li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-2123 bricks-menu-item"><a href="https://aachips.co/single/">Posts</a></li>
<li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-2148 bricks-menu-item"><a href="https://aachips.co/about/">About</a></li>
<li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-2125 bricks-menu-item"><a href="https://aachips.co/booking/">Meet</a></li>
<li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-2127 bricks-menu-item"><a href="https://aachips.co/support/">Support</a></li>
<li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-2128 bricks-menu-item"><a href="https://aachips.co/newsletter/">News</a></li>
</ul></nav><div class="bricks-mobile-menu-overlay"></div></div></div><div id="brxe-fca855" class="brxe-block bricks-lazy-hidden"><ul id="brxe-6331c8" class="brxe-social-icons"><li class="repeater-item no-link"><svg class="" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"  viewBox="0 0 540 84.5"><defs><style>.cls-1{fill:url(#linear-gradient);}.cls-2{fill:#fff;}.cls-3{fill:#ff5e5b;}.cls-4{fill:url(#linear-gradient-2);}</style><linearGradient id="linear-gradient" x1="0" y1="42.25" x2="540" y2="42.25" gradientTransform="matrix(1, 0, 0, 1, 0, 0)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#ff4ea3"></stop><stop offset="1" stop-color="#ff5e5b"></stop></linearGradient><linearGradient id="linear-gradient-2" x1="34.1" y1="479.58" x2="44.48" y2="447.72" gradientTransform="translate(4.21 490.11) scale(1 -1)" xlink:href="#linear-gradient"></linearGradient></defs><rect class="cls-1" y="0" width="540" height="84.5" rx="13.09" ry="13.09"></rect><g id="Layer_1-2"><g id="Layer_1-3"><g><path class="cls-2" d="M79.95,45.15c-2.89,.32-5.3,.15-5.3,.15V27.18h3.69c2.41,0,4.66,.96,6.25,2.89,1.12,1.29,1.93,3.05,1.93,5.62,0,5.93-3.05,8.33-6.57,9.45Zm15.56-12.67c-1.12-5.62-4.01-9.14-7.22-11.23-3.2-2.25-7.06-3.37-10.91-3.37H22.19c-1.93,0-2.73,1.93-2.73,2.89v.64s-.16,24.39,.15,37.54c.48,7.7,8.18,7.7,8.18,7.7,0,0,25.18,0,37.38-.15,.64,0,1.12,0,1.76-.15,6.89-1.76,7.54-8.18,7.54-11.72,13.81,.62,23.6-9.17,21.04-22.16Z"></path><g><path d="M97.51,32.1h0v-.03c-1.37-6.87-5.14-10.59-8.07-12.5-3.43-2.4-7.7-3.72-12.05-3.72H22.19c-3.1,0-4.76,2.86-4.76,4.92v.63c0,.24-.15,24.56,.15,37.6,0,.03,0,.05,0,.08,.31,4.96,3.11,7.33,5.41,8.44,2.38,1.15,4.7,1.17,4.8,1.17h0c1.03,0,25.42,0,37.38-.15,.69,0,1.35,0,2.22-.21h.03c3.64-.94,6.23-3.07,7.7-6.33,.7-1.54,1.12-3.28,1.28-5.31,3.34-.08,6.47-.74,9.33-1.96,3.01-1.28,5.56-3.12,7.58-5.47,3.96-4.6,5.44-10.7,4.17-17.16ZM21.65,58.87c-.3-12.82-.16-36.45-.15-37.46v-.63c0-.06,.15-.87,.7-.87h55.2c3.53,0,6.99,1.07,9.74,3,.02,.01,.04,.03,.06,.04,3.32,2.16,5.45,5.5,6.33,9.92,1.03,5.22-.13,10.1-3.26,13.74-3.53,4.1-9.25,6.29-15.69,6-.55-.03-1.1,.18-1.5,.56-.4,.38-.63,.92-.63,1.47,0,5.52-1.96,8.71-5.99,9.74-.4,.1-.69,.1-1.26,.1h-.04c-10.61,.13-31.1,.15-37.13,.15h-.22s-1.52-.03-3.03-.76c-1.92-.93-2.97-2.61-3.12-5Z"></path><path d="M74.51,47.33c.07,0,.73,.05,1.76,.05,1.33,0,2.65-.07,3.9-.21,.13-.01,.26-.04,.39-.08,5.3-1.68,7.99-5.52,7.99-11.39,0-2.76-.81-5.09-2.41-6.93-1.94-2.33-4.71-3.62-7.8-3.62h-3.69c-1.12,0-2.03,.91-2.03,2.03v18.12c0,1.06,.83,1.95,1.89,2.03Zm2.17-4.02v-14.1h1.66c1.87,0,3.54,.77,4.69,2.16l.03,.04c.96,1.11,1.43,2.51,1.43,4.29,0,4.06-1.49,6.29-4.97,7.46-.91,.09-1.86,.15-2.84,.16Z"></path></g></g><path class="cls-3" d="M46.57,56.38c.64,.32,.96,0,.96,0,0,0,8.82-8.02,12.84-12.67,3.52-4.17,3.69-11.07-2.25-13.63-6.1-2.57-11.07,3.05-11.07,3.05-4.33-4.81-10.91-4.49-13.96-1.29s-1.93,8.67,.32,11.87c2.08,2.89,11.39,11.23,12.84,12.52,0-.17,.17,0,.32,.15Z"></path><path class="cls-4" d="M46.87,56.61c.36,.07,.67-.24,.67-.24,0,0,8.82-8.02,12.84-12.67,3.52-4.17,3.69-11.07-2.25-13.63-6.1-2.57-11.07,3.05-11.07,3.05-4.33-4.81-10.91-4.49-13.96-1.29s-1.93,8.67,.32,11.87c1.96,2.73,11.81,11.59,13.04,12.69,.12,.11,.26,.19,.42,.21Z"></path></g></g><g><path class="cls-2" d="M132.83,53.94c-1.88-.56-3.4-1.32-4.58-2.28-.63-.46-.94-1.12-.94-1.97,0-.58,.17-1.07,.51-1.5,.34-.42,.75-.64,1.21-.64,.49,0,1.05,.19,1.68,.58,1.18,.85,2.41,1.46,3.7,1.85,1.29,.38,2.71,.58,4.27,.58,2,0,3.53-.35,4.6-1.05,1.07-.7,1.6-1.72,1.6-3.06,0-1.04-.5-1.84-1.5-2.4-1-.56-2.66-1.1-4.99-1.62-2.41-.52-4.37-1.12-5.87-1.81-1.51-.68-2.64-1.56-3.41-2.63-.77-1.07-1.15-2.42-1.15-4.07s.48-3.22,1.44-4.56c.96-1.34,2.29-2.39,4.01-3.14,1.71-.75,3.64-1.13,5.77-1.13,3.92,0,7.11,1.04,9.57,3.12,.36,.3,.61,.6,.76,.88,.15,.29,.23,.65,.23,1.09,0,.58-.17,1.08-.51,1.5-.34,.42-.75,.64-1.21,.64-.27,0-.53-.04-.76-.12-.23-.08-.54-.23-.92-.45-1.12-.82-2.21-1.43-3.27-1.83-1.05-.4-2.35-.6-3.88-.6-1.86,0-3.33,.38-4.42,1.13-1.08,.75-1.62,1.8-1.62,3.14,0,1.12,.48,1.99,1.44,2.59,.96,.6,2.59,1.17,4.89,1.68,2.44,.55,4.42,1.16,5.94,1.83,1.52,.67,2.68,1.53,3.49,2.57,.81,1.04,1.21,2.34,1.21,3.9,0,1.7-.47,3.2-1.42,4.5s-2.27,2.31-3.98,3.04c-1.71,.72-3.68,1.09-5.9,1.09s-4.1-.28-5.98-.84Z"></path><path class="cls-2" d="M172.28,34.57c.47,.44,.7,1.03,.7,1.77v15.94c0,.71-.24,1.29-.72,1.73-.48,.44-1.1,.66-1.87,.66s-1.33-.21-1.77-.62c-.44-.41-.66-.97-.66-1.68v-1.03c-.63,1.1-1.48,1.94-2.55,2.53-1.07,.59-2.27,.88-3.62,.88-4.93,0-7.39-2.77-7.39-8.3v-10.11c0-.74,.23-1.33,.7-1.77,.47-.44,1.08-.66,1.85-.66s1.42,.22,1.89,.66c.47,.44,.7,1.03,.7,1.77v10.15c0,1.42,.29,2.48,.86,3.16,.58,.68,1.48,1.03,2.71,1.03,1.42,0,2.57-.47,3.43-1.42,.86-.94,1.29-2.2,1.29-3.76v-9.16c0-.74,.23-1.33,.7-1.77,.47-.44,1.08-.66,1.85-.66s1.42,.22,1.89,.66Z"></path><path class="cls-2" d="M194.49,35.15c1.36,.88,2.41,2.12,3.16,3.72,.75,1.6,1.13,3.44,1.13,5.53s-.38,3.9-1.13,5.46c-.75,1.56-1.8,2.77-3.14,3.62-1.34,.85-2.89,1.27-4.64,1.27-1.42,0-2.7-.3-3.82-.9-1.12-.6-1.99-1.44-2.59-2.51v8.38c0,.71-.23,1.27-.7,1.68-.47,.41-1.08,.62-1.85,.62s-1.39-.22-1.87-.66c-.48-.44-.72-1.01-.72-1.73v-23.29c0-.74,.23-1.33,.68-1.77,.45-.44,1.06-.66,1.83-.66s1.38,.22,1.85,.66c.47,.44,.7,1.03,.7,1.77v1.03c.6-1.12,1.47-1.99,2.61-2.61,1.14-.62,2.43-.92,3.88-.92,1.73,0,3.27,.44,4.62,1.31Zm-2.2,13.95c.9-1.11,1.36-2.68,1.36-4.7s-.45-3.7-1.36-4.85c-.9-1.15-2.16-1.73-3.78-1.73s-2.91,.56-3.8,1.68c-.89,1.12-1.33,2.73-1.33,4.81s.44,3.64,1.33,4.77c.89,1.12,2.16,1.68,3.8,1.68s2.88-.55,3.78-1.66Z"></path><path class="cls-2" d="M219.18,35.15c1.36,.88,2.41,2.12,3.16,3.72,.75,1.6,1.13,3.44,1.13,5.53s-.38,3.9-1.13,5.46c-.75,1.56-1.8,2.77-3.14,3.62-1.34,.85-2.89,1.27-4.64,1.27-1.42,0-2.7-.3-3.82-.9-1.12-.6-1.99-1.44-2.59-2.51v8.38c0,.71-.23,1.27-.7,1.68-.47,.41-1.08,.62-1.85,.62s-1.39-.22-1.87-.66c-.48-.44-.72-1.01-.72-1.73v-23.29c0-.74,.23-1.33,.68-1.77,.45-.44,1.06-.66,1.83-.66s1.38,.22,1.85,.66c.47,.44,.7,1.03,.7,1.77v1.03c.6-1.12,1.47-1.99,2.61-2.61,1.14-.62,2.43-.92,3.88-.92,1.73,0,3.27,.44,4.62,1.31Zm-2.2,13.95c.9-1.11,1.36-2.68,1.36-4.7s-.45-3.7-1.36-4.85c-.9-1.15-2.16-1.73-3.78-1.73s-2.91,.56-3.8,1.68c-.89,1.12-1.33,2.73-1.33,4.81s.44,3.64,1.33,4.77c.89,1.12,2.16,1.68,3.8,1.68s2.88-.55,3.78-1.66Z"></path><path class="cls-2" d="M231.46,53.47c-1.55-.85-2.75-2.07-3.59-3.66-.85-1.59-1.27-3.44-1.27-5.55s.42-3.95,1.27-5.52c.85-1.58,2.05-2.79,3.59-3.64,1.55-.85,3.35-1.27,5.4-1.27s3.85,.42,5.4,1.27,2.74,2.06,3.57,3.64c.83,1.58,1.25,3.42,1.25,5.52s-.42,3.96-1.25,5.55c-.84,1.59-2.03,2.81-3.57,3.66-1.55,.85-3.35,1.27-5.4,1.27s-3.85-.42-5.4-1.27Zm9.18-4.35c.88-1.09,1.31-2.71,1.31-4.85s-.44-3.72-1.31-4.83c-.88-1.11-2.14-1.66-3.78-1.66s-2.91,.55-3.8,1.66c-.89,1.11-1.33,2.72-1.33,4.83s.44,3.75,1.31,4.85c.88,1.1,2.14,1.64,3.78,1.64s2.94-.55,3.82-1.64Z"></path><path class="cls-2" d="M264.22,34.37c.41,.36,.62,.89,.62,1.6s-.18,1.29-.53,1.64c-.36,.36-1,.59-1.93,.7l-1.23,.12c-1.62,.16-2.8,.71-3.55,1.64-.75,.93-1.13,2.09-1.13,3.49v8.63c0,.79-.25,1.4-.74,1.83-.49,.42-1.11,.64-1.85,.64s-1.35-.21-1.83-.64c-.48-.42-.72-1.03-.72-1.83v-15.9c0-.77,.24-1.36,.72-1.77,.48-.41,1.07-.62,1.79-.62s1.29,.2,1.73,.6c.44,.4,.66,.96,.66,1.7v1.64c.52-1.21,1.29-2.14,2.32-2.79,1.03-.66,2.17-1.03,3.43-1.11l.58-.04c.71-.05,1.27,.1,1.68,.45Z"></path><path class="cls-2" d="M281.39,52.69c0,.66-.27,1.16-.8,1.5-.53,.34-1.29,.49-2.28,.43l-1.11-.08c-4.6-.33-6.9-2.79-6.9-7.39v-8.96h-2.05c-.74,0-1.31-.17-1.7-.49-.4-.33-.6-.81-.6-1.44s.2-1.11,.6-1.44c.4-.33,.97-.49,1.7-.49h2.05v-3.78c0-.74,.23-1.33,.7-1.77,.47-.44,1.1-.66,1.89-.66s1.38,.22,1.85,.66c.47,.44,.7,1.03,.7,1.77v3.78h3.49c.74,0,1.31,.17,1.7,.49,.4,.33,.6,.81,.6,1.44s-.2,1.11-.6,1.44c-.4,.33-.97,.49-1.7,.49h-3.49v9.33c0,2.03,.93,3.11,2.79,3.25l1.11,.08c1.37,.08,2.05,.7,2.05,1.85Z"></path><path class="cls-2" d="M324.1,35.79c1.16,1.3,1.75,3.4,1.75,6.31v10.11c0,.79-.24,1.4-.72,1.83-.48,.42-1.1,.64-1.87,.64s-1.35-.21-1.83-.64c-.48-.42-.72-1.03-.72-1.83v-10.11c0-1.51-.27-2.58-.8-3.22s-1.35-.97-2.44-.97c-1.34,0-2.41,.46-3.2,1.38-.79,.92-1.19,2.17-1.19,3.76v9.16c0,.79-.24,1.4-.72,1.83-.48,.42-1.09,.64-1.83,.64s-1.36-.21-1.85-.64c-.49-.42-.74-1.03-.74-1.83v-10.11c0-1.51-.27-2.58-.8-3.22s-1.35-.97-2.44-.97c-1.34,0-2.4,.46-3.18,1.38-.78,.92-1.17,2.17-1.17,3.76v9.16c0,.79-.25,1.4-.74,1.83-.49,.42-1.11,.64-1.85,.64s-1.35-.21-1.83-.64c-.48-.42-.72-1.03-.72-1.83v-15.9c0-.77,.25-1.36,.74-1.77,.49-.41,1.11-.62,1.85-.62s1.29,.2,1.75,.6c.45,.4,.68,.96,.68,1.7v.99c.63-1.09,1.47-1.93,2.53-2.51,1.05-.58,2.27-.86,3.64-.86,3.01,0,5.02,1.26,6.04,3.78,.6-1.15,1.49-2.07,2.67-2.75,1.18-.68,2.51-1.03,3.98-1.03,2.19,0,3.87,.65,5.03,1.95Z"></path><path class="cls-2" d="M348.09,49.45c.29,.36,.43,.84,.43,1.44,0,.85-.51,1.56-1.52,2.14-.93,.52-1.99,.94-3.16,1.25-1.18,.31-2.3,.47-3.37,.47-3.23,0-5.79-.93-7.68-2.79s-2.83-4.41-2.83-7.64c0-2.05,.41-3.88,1.23-5.46s1.98-2.82,3.47-3.7c1.49-.88,3.18-1.31,5.07-1.31s3.38,.4,4.72,1.19c1.34,.79,2.38,1.92,3.12,3.37,.74,1.45,1.11,3.16,1.11,5.13,0,1.18-.52,1.77-1.56,1.77h-12.12c.16,1.89,.7,3.28,1.6,4.17,.9,.89,2.22,1.33,3.94,1.33,.88,0,1.65-.11,2.32-.33,.67-.22,1.43-.52,2.28-.9,.82-.44,1.42-.66,1.81-.66,.47,0,.84,.18,1.13,.53Zm-11.58-10.68c-.84,.88-1.33,2.14-1.5,3.78h9.28c-.06-1.67-.47-2.94-1.23-3.8-.77-.86-1.83-1.29-3.2-1.29s-2.51,.44-3.35,1.32Z"></path><path class="cls-2" d="M367.76,53.47c-1.55-.85-2.75-2.07-3.59-3.66-.85-1.59-1.27-3.44-1.27-5.55s.42-3.95,1.27-5.52c.85-1.58,2.05-2.79,3.59-3.64,1.55-.85,3.35-1.27,5.4-1.27s3.85,.42,5.4,1.27,2.74,2.06,3.57,3.64c.83,1.58,1.25,3.42,1.25,5.52s-.42,3.96-1.25,5.55c-.84,1.59-2.03,2.81-3.57,3.66-1.55,.85-3.35,1.27-5.4,1.27s-3.85-.42-5.4-1.27Zm9.18-4.35c.88-1.09,1.31-2.71,1.31-4.85s-.44-3.72-1.31-4.83c-.88-1.11-2.14-1.66-3.78-1.66s-2.91,.55-3.8,1.66c-.89,1.11-1.33,2.72-1.33,4.83s.44,3.75,1.31,4.85c.88,1.1,2.14,1.64,3.78,1.64s2.94-.55,3.82-1.64Z"></path><path class="cls-2" d="M404.67,35.89c1.18,1.37,1.77,3.44,1.77,6.2v10.11c0,.77-.23,1.37-.68,1.81-.45,.44-1.08,.66-1.87,.66s-1.42-.22-1.89-.66c-.47-.44-.7-1.04-.7-1.81v-9.82c0-1.56-.29-2.7-.88-3.41-.59-.71-1.51-1.07-2.77-1.07-1.48,0-2.66,.47-3.55,1.4-.89,.93-1.33,2.18-1.33,3.74v9.16c0,.77-.23,1.37-.7,1.81-.47,.44-1.1,.66-1.89,.66s-1.42-.22-1.87-.66c-.45-.44-.68-1.04-.68-1.81v-15.9c0-.71,.23-1.29,.7-1.73,.47-.44,1.09-.66,1.89-.66,.71,0,1.29,.21,1.75,.64,.45,.42,.68,.98,.68,1.66v1.11c.68-1.12,1.6-1.99,2.75-2.59,1.15-.6,2.45-.9,3.9-.9,2.41,0,4.2,.68,5.38,2.05Z"></path><path class="cls-2" d="M446.54,52.2c0,.63-.24,1.19-.72,1.66-.48,.48-1.05,.72-1.7,.72s-1.32-.29-1.89-.86l-13.68-12.82v11.09c0,.79-.24,1.44-.72,1.93s-1.13,.74-1.95,.74-1.43-.25-1.91-.74-.72-1.14-.72-1.93V27.88c0-.79,.24-1.43,.72-1.91,.48-.48,1.12-.72,1.91-.72s1.47,.24,1.95,.72c.48,.48,.72,1.12,.72,1.91v10.56l13.23-12.41c.49-.49,1.05-.74,1.68-.74s1.18,.24,1.66,.72c.48,.48,.72,1.03,.72,1.66s-.27,1.21-.82,1.73l-11.05,10.11,11.75,10.93c.55,.52,.82,1.11,.82,1.77Z"></path><path class="cls-2" d="M453.46,53.47c-1.55-.85-2.75-2.07-3.59-3.66-.85-1.59-1.27-3.44-1.27-5.55s.42-3.95,1.27-5.52c.85-1.58,2.05-2.79,3.59-3.64s3.35-1.27,5.4-1.27,3.85,.42,5.4,1.27c1.55,.85,2.74,2.06,3.57,3.64,.84,1.58,1.25,3.42,1.25,5.52s-.42,3.96-1.25,5.55c-.83,1.59-2.03,2.81-3.57,3.66-1.55,.85-3.35,1.27-5.4,1.27s-3.86-.42-5.4-1.27Zm9.18-4.35c.88-1.09,1.31-2.71,1.31-4.85s-.44-3.72-1.31-4.83c-.88-1.11-2.14-1.66-3.78-1.66s-2.91,.55-3.8,1.66c-.89,1.11-1.33,2.72-1.33,4.83s.44,3.75,1.31,4.85c.88,1.1,2.14,1.64,3.78,1.64s2.94-.55,3.82-1.64Z"></path><path class="cls-2" d="M474.16,44.93c-.4-.36-.6-.85-.6-1.48s.2-1.13,.6-1.5c.4-.37,.99-.55,1.79-.55h8.46c.79,0,1.39,.18,1.79,.55,.4,.37,.6,.87,.6,1.5s-.2,1.12-.6,1.48c-.4,.36-.99,.53-1.79,.53h-8.46c-.79,0-1.39-.18-1.79-.53Z"></path><path class="cls-2" d="M511.57,36.26v15.94c0,.79-.24,1.4-.72,1.83-.48,.42-1.09,.64-1.83,.64s-1.36-.21-1.85-.64c-.49-.42-.74-1.03-.74-1.83v-14.01h-8.13v14.01c0,.79-.24,1.4-.72,1.81-.48,.41-1.09,.62-1.83,.62s-1.36-.21-1.85-.62-.74-1.01-.74-1.81v-14.01h-2.05c-1.53,0-2.3-.64-2.3-1.93s.77-1.93,2.3-1.93h2.05v-.21c0-2.55,.68-4.57,2.03-6.06,1.36-1.49,3.27-2.35,5.73-2.57l.95-.08c.14-.03,.34-.04,.62-.04,1.45,0,2.18,.62,2.18,1.85,0,.6-.16,1.07-.49,1.4-.33,.33-.78,.52-1.36,.58l-.9,.08c-1.26,.11-2.18,.5-2.75,1.17-.58,.67-.86,1.65-.86,2.94v.94h10.97c1.53,0,2.3,.64,2.3,1.93Zm-4.77-6.76c-.55-.51-.82-1.17-.82-1.99s.27-1.48,.82-1.97,1.29-.74,2.22-.74,1.64,.25,2.2,.74c.56,.49,.84,1.15,.84,1.97s-.28,1.49-.84,1.99c-.56,.51-1.29,.76-2.2,.76s-1.67-.25-2.22-.76Z"></path></g></svg></li></ul></div></div></section></header><main id="brx-content"><section id="brxe-oevovo" class="brxe-section bricks-lazy-hidden"><div id="brxe-utxbtf" class="brxe-container bricks-lazy-hidden"><h1 id="brxe-sdcanc" class="brxe-heading">Fight for Virtual Services in Mental Healthcare, Medical Advocacy, and Peer Support</h1><h2 id="brxe-nzeriv" class="brxe-heading">Why Virtual Care Matters</h2><div id="brxe-hyuxzx" class="brxe-text"><div>
<div>Telehealth and online platforms made crucial support readily available, empowering individuals, especially those from vulnerable communities, to prioritize their well-being. However, a worrying trend is taking hold: Managed Care Organizations (MCOs) are de-funding and ending virtual service access, both for workers and clients.</div>
</div>
<div>
<ul class="impact-groups">
<li><strong>Single parent households:</strong> Juggling work, childcare, and appointments can be overwhelming. Virtual options offer flexibility and convenience, ensuring care doesn&#8217;t fall through the cracks.</li>
<li><strong>Disabled individuals:</strong> Mobility limitations and discomfort leaving the house shouldn&#8217;t be barriers to healthcare. Virtual visits enable equal access from the comfort of home.</li>
<li><strong>Survivors of domestic violence:</strong> Isolation is a common tactic used by abusers. Virtual support provides a safe, confidential space for connection and empowerment.</li>
</ul>
</div>
</div><h2 id="brxe-dhodyc" class="brxe-heading">The Devastating Impact of Ending Virtual Care</h2><div id="brxe-zxjzof" class="brxe-text"><div>
<div>MCO decisions to eliminate virtual services are harmful. They limit access to care, increase isolation for vulnerable populations, and create undue stress and burden.</div>
</div>
</div><h2 id="brxe-fcwasj" class="brxe-heading">Documenting the Issue</h2><div id="brxe-xmgywx" class="brxe-text"><div>
<div>We need to document the extent of this problem. Share your story! Let us know:</div>
</div>
</div><div id="brxe-pykual" class="brxe-container bricks-lazy-hidden"><h2 id="brxe-sdxuyf" class="brxe-heading">Join the Movement!</h2>		<form id="brxe-jzmbvy" data-script-id="jzmbvy" class="brxe-form" method="post" data-element-id="jzmbvy">
			
				<div class="form-group" role="group" aria-labelledby="label-b323bd">
				<input id="form-field-aeee5e" name="form-field-b323bd" aria-label="Full Name*:" type="text" value placeholder="Your Name" spellcheck="false">
				
				
							</div>
				
				<div class="form-group" role="group" aria-labelledby="label-2a8980">
				<input id="form-field-fb7506" name="form-field-2a8980" aria-label="Email Address*:" type="email" value placeholder="Your Email" required>
				
				
							</div>
				
				<div class="form-group" role="group" aria-labelledby="label-zebroj">
				
								<textarea id="form-field-ef6569" name="form-field-zebroj" aria-label="Tell us about your experiences with virtual care (optional):" placeholder="Tell Us About Your Experiences with Virtual Care (Optional)" spellcheck="false"></textarea>
				
				
							</div>
				
				<div class="form-group" role="radiogroup" aria-labelledby="label-cfa101">
				<div class="label" id="label-cfa101">Role in the System*:</div>
				
				
								<ul class="options-wrapper" >
															<li>
						<input
							type="radio"
							id="form-field-cfa101-0"
							name="form-field-cfa101[]"
							required							value="Patient">
							<label for="form-field-cfa101-0">Patient</label>
					</li>
										<li>
						<input
							type="radio"
							id="form-field-cfa101-1"
							name="form-field-cfa101[]"
							required							value="Family Member/Loved One">
							<label for="form-field-cfa101-1">Family Member/Loved One</label>
					</li>
										<li>
						<input
							type="radio"
							id="form-field-cfa101-2"
							name="form-field-cfa101[]"
							required							value="Healthcare Professional">
							<label for="form-field-cfa101-2">Healthcare Professional</label>
					</li>
										<li>
						<input
							type="radio"
							id="form-field-cfa101-3"
							name="form-field-cfa101[]"
							required							value="Policy Maker/ Decision Maker">
							<label for="form-field-cfa101-3">Policy Maker/ Decision Maker</label>
					</li>
										<li>
						<input
							type="radio"
							id="form-field-cfa101-4"
							name="form-field-cfa101[]"
							required							value="Other">
							<label for="form-field-cfa101-4">Other</label>
					</li>
									</ul>
							</div>
				
		  <div class="form-group submit-button-wrapper">
				<button type="submit" class="bricks-button bricks-background-primary">
					<span class="text">Sign &amp; Submit</span><span class="loading"><svg version="1.1" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><g stroke-linecap="round" stroke-width="1" stroke="currentColor" fill="none" stroke-linejoin="round"><path d="M0.927,10.199l2.787,4.151l3.205,-3.838"></path><path d="M23.5,14.5l-2.786,-4.15l-3.206,3.838"></path><path d="M20.677,10.387c0.834,4.408 -2.273,8.729 -6.509,9.729c-2.954,0.699 -5.916,-0.238 -7.931,-2.224"></path><path d="M3.719,14.325c-1.314,-4.883 1.969,-9.675 6.538,-10.753c3.156,-0.747 6.316,0.372 8.324,2.641"></path></g><path fill="none" d="M0,0h24v24h-24Z"></path></svg>
</span>				</button>
			</div>
		</form>
		<div id="brxe-icgbmc" class="brxe-text"><p>Sign the Petition: Let your voice be heard! Demand that MCOs restore and expand virtual service access.</p>
</div><div id="brxe-sconuo" class="brxe-text"><div>
<div>Share this page using the following hashtags:  #TelehealthForAll #AccessIsEssential #VulnerableCommunities</div>
</div>
</div></div></div></section></main><footer id="brx-footer"><div id="brxe-3c7195" class="brxe-container bricks-lazy-hidden"><div id="brxe-fb6f9e" class="brxe-container bricks-lazy-hidden"><div id="brxe-008f66" class="brxe-container bricks-lazy-hidden"><h2 id="brxe-bb8b39" class="brxe-heading">No Newsletter</h2><div id="brxe-8df127" class="brxe-text"><p>just if you&#8217;d like to stay in touch.&nbsp;</p>
</div>		<form id="brxe-f220b3" data-script-id="f220b3" class="brxe-form" method="post" data-element-id="f220b3">
			
				<div class="form-group" role="group" aria-labelledby="label-287eee">
				<input id="form-field-a29954" name="form-field-287eee" aria-label="Name" type="text" value placeholder="Name" spellcheck="false" required>
				
				
							</div>
				
				<div class="form-group" role="group" aria-labelledby="label-abaawo">
				<input id="form-field-fc433c" name="form-field-abaawo" aria-label="Email" type="email" value placeholder="Your email address" required>
				
				
							</div>
				
		  <div class="form-group submit-button-wrapper">
				<button type="submit" class="bricks-button bricks-background-secondary md">
					<span class="text">Subscribe</span><span class="loading"><svg version="1.1" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><g stroke-linecap="round" stroke-width="1" stroke="currentColor" fill="none" stroke-linejoin="round"><path d="M0.927,10.199l2.787,4.151l3.205,-3.838"></path><path d="M23.5,14.5l-2.786,-4.15l-3.206,3.838"></path><path d="M20.677,10.387c0.834,4.408 -2.273,8.729 -6.509,9.729c-2.954,0.699 -5.916,-0.238 -7.931,-2.224"></path><path d="M3.719,14.325c-1.314,-4.883 1.969,-9.675 6.538,-10.753c3.156,-0.747 6.316,0.372 8.324,2.641"></path></g><path fill="none" d="M0,0h24v24h-24Z"></path></svg>
</span>				</button>
			</div>
		</form>
		<div id="brxe-c82c01" class="brxe-text"><p>We don’t spam! Unsubscribe anytime.</p>
</div></div><div id="brxe-beff6d" class="brxe-container bricks-lazy-hidden"><h3 id="brxe-7ed787" class="brxe-heading">Apple Chip Blog</h3><div id="brxe-qlfuak" class="brxe-text"><p>Other Projects</p>
</div><ul id="brxe-lewqfb" class="brxe-list"><li ><div class="content"><span class="title">Coming Soon</span><span class="separator"></span></div></li></ul></div><div id="brxe-4d502c" class="brxe-container bricks-lazy-hidden"><h3 id="brxe-52277c" class="brxe-heading">Support</h3><div id="brxe-zykzqz" class="brxe-text"><p>Make a one-time or recurring monthly donation to support and boost this project!</p>
</div><ul id="brxe-2a5920" class="brxe-social-icons"><li class="repeater-item no-link"><svg class="" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"  viewBox="0 0 540 84.5"><defs><style>.cls-1{fill:url(#linear-gradient);}.cls-2{fill:#fff;}.cls-3{fill:#ff5e5b;}.cls-4{fill:url(#linear-gradient-2);}</style><linearGradient id="linear-gradient" x1="0" y1="42.25" x2="540" y2="42.25" gradientTransform="matrix(1, 0, 0, 1, 0, 0)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#ff4ea3"></stop><stop offset="1" stop-color="#ff5e5b"></stop></linearGradient><linearGradient id="linear-gradient-2" x1="34.1" y1="479.58" x2="44.48" y2="447.72" gradientTransform="translate(4.21 490.11) scale(1 -1)" xlink:href="#linear-gradient"></linearGradient></defs><rect class="cls-1" y="0" width="540" height="84.5" rx="13.09" ry="13.09"></rect><g id="Layer_1-2"><g id="Layer_1-3"><g><path class="cls-2" d="M79.95,45.15c-2.89,.32-5.3,.15-5.3,.15V27.18h3.69c2.41,0,4.66,.96,6.25,2.89,1.12,1.29,1.93,3.05,1.93,5.62,0,5.93-3.05,8.33-6.57,9.45Zm15.56-12.67c-1.12-5.62-4.01-9.14-7.22-11.23-3.2-2.25-7.06-3.37-10.91-3.37H22.19c-1.93,0-2.73,1.93-2.73,2.89v.64s-.16,24.39,.15,37.54c.48,7.7,8.18,7.7,8.18,7.7,0,0,25.18,0,37.38-.15,.64,0,1.12,0,1.76-.15,6.89-1.76,7.54-8.18,7.54-11.72,13.81,.62,23.6-9.17,21.04-22.16Z"></path><g><path d="M97.51,32.1h0v-.03c-1.37-6.87-5.14-10.59-8.07-12.5-3.43-2.4-7.7-3.72-12.05-3.72H22.19c-3.1,0-4.76,2.86-4.76,4.92v.63c0,.24-.15,24.56,.15,37.6,0,.03,0,.05,0,.08,.31,4.96,3.11,7.33,5.41,8.44,2.38,1.15,4.7,1.17,4.8,1.17h0c1.03,0,25.42,0,37.38-.15,.69,0,1.35,0,2.22-.21h.03c3.64-.94,6.23-3.07,7.7-6.33,.7-1.54,1.12-3.28,1.28-5.31,3.34-.08,6.47-.74,9.33-1.96,3.01-1.28,5.56-3.12,7.58-5.47,3.96-4.6,5.44-10.7,4.17-17.16ZM21.65,58.87c-.3-12.82-.16-36.45-.15-37.46v-.63c0-.06,.15-.87,.7-.87h55.2c3.53,0,6.99,1.07,9.74,3,.02,.01,.04,.03,.06,.04,3.32,2.16,5.45,5.5,6.33,9.92,1.03,5.22-.13,10.1-3.26,13.74-3.53,4.1-9.25,6.29-15.69,6-.55-.03-1.1,.18-1.5,.56-.4,.38-.63,.92-.63,1.47,0,5.52-1.96,8.71-5.99,9.74-.4,.1-.69,.1-1.26,.1h-.04c-10.61,.13-31.1,.15-37.13,.15h-.22s-1.52-.03-3.03-.76c-1.92-.93-2.97-2.61-3.12-5Z"></path><path d="M74.51,47.33c.07,0,.73,.05,1.76,.05,1.33,0,2.65-.07,3.9-.21,.13-.01,.26-.04,.39-.08,5.3-1.68,7.99-5.52,7.99-11.39,0-2.76-.81-5.09-2.41-6.93-1.94-2.33-4.71-3.62-7.8-3.62h-3.69c-1.12,0-2.03,.91-2.03,2.03v18.12c0,1.06,.83,1.95,1.89,2.03Zm2.17-4.02v-14.1h1.66c1.87,0,3.54,.77,4.69,2.16l.03,.04c.96,1.11,1.43,2.51,1.43,4.29,0,4.06-1.49,6.29-4.97,7.46-.91,.09-1.86,.15-2.84,.16Z"></path></g></g><path class="cls-3" d="M46.57,56.38c.64,.32,.96,0,.96,0,0,0,8.82-8.02,12.84-12.67,3.52-4.17,3.69-11.07-2.25-13.63-6.1-2.57-11.07,3.05-11.07,3.05-4.33-4.81-10.91-4.49-13.96-1.29s-1.93,8.67,.32,11.87c2.08,2.89,11.39,11.23,12.84,12.52,0-.17,.17,0,.32,.15Z"></path><path class="cls-4" d="M46.87,56.61c.36,.07,.67-.24,.67-.24,0,0,8.82-8.02,12.84-12.67,3.52-4.17,3.69-11.07-2.25-13.63-6.1-2.57-11.07,3.05-11.07,3.05-4.33-4.81-10.91-4.49-13.96-1.29s-1.93,8.67,.32,11.87c1.96,2.73,11.81,11.59,13.04,12.69,.12,.11,.26,.19,.42,.21Z"></path></g></g><g><path class="cls-2" d="M132.83,53.94c-1.88-.56-3.4-1.32-4.58-2.28-.63-.46-.94-1.12-.94-1.97,0-.58,.17-1.07,.51-1.5,.34-.42,.75-.64,1.21-.64,.49,0,1.05,.19,1.68,.58,1.18,.85,2.41,1.46,3.7,1.85,1.29,.38,2.71,.58,4.27,.58,2,0,3.53-.35,4.6-1.05,1.07-.7,1.6-1.72,1.6-3.06,0-1.04-.5-1.84-1.5-2.4-1-.56-2.66-1.1-4.99-1.62-2.41-.52-4.37-1.12-5.87-1.81-1.51-.68-2.64-1.56-3.41-2.63-.77-1.07-1.15-2.42-1.15-4.07s.48-3.22,1.44-4.56c.96-1.34,2.29-2.39,4.01-3.14,1.71-.75,3.64-1.13,5.77-1.13,3.92,0,7.11,1.04,9.57,3.12,.36,.3,.61,.6,.76,.88,.15,.29,.23,.65,.23,1.09,0,.58-.17,1.08-.51,1.5-.34,.42-.75,.64-1.21,.64-.27,0-.53-.04-.76-.12-.23-.08-.54-.23-.92-.45-1.12-.82-2.21-1.43-3.27-1.83-1.05-.4-2.35-.6-3.88-.6-1.86,0-3.33,.38-4.42,1.13-1.08,.75-1.62,1.8-1.62,3.14,0,1.12,.48,1.99,1.44,2.59,.96,.6,2.59,1.17,4.89,1.68,2.44,.55,4.42,1.16,5.94,1.83,1.52,.67,2.68,1.53,3.49,2.57,.81,1.04,1.21,2.34,1.21,3.9,0,1.7-.47,3.2-1.42,4.5s-2.27,2.31-3.98,3.04c-1.71,.72-3.68,1.09-5.9,1.09s-4.1-.28-5.98-.84Z"></path><path class="cls-2" d="M172.28,34.57c.47,.44,.7,1.03,.7,1.77v15.94c0,.71-.24,1.29-.72,1.73-.48,.44-1.1,.66-1.87,.66s-1.33-.21-1.77-.62c-.44-.41-.66-.97-.66-1.68v-1.03c-.63,1.1-1.48,1.94-2.55,2.53-1.07,.59-2.27,.88-3.62,.88-4.93,0-7.39-2.77-7.39-8.3v-10.11c0-.74,.23-1.33,.7-1.77,.47-.44,1.08-.66,1.85-.66s1.42,.22,1.89,.66c.47,.44,.7,1.03,.7,1.77v10.15c0,1.42,.29,2.48,.86,3.16,.58,.68,1.48,1.03,2.71,1.03,1.42,0,2.57-.47,3.43-1.42,.86-.94,1.29-2.2,1.29-3.76v-9.16c0-.74,.23-1.33,.7-1.77,.47-.44,1.08-.66,1.85-.66s1.42,.22,1.89,.66Z"></path><path class="cls-2" d="M194.49,35.15c1.36,.88,2.41,2.12,3.16,3.72,.75,1.6,1.13,3.44,1.13,5.53s-.38,3.9-1.13,5.46c-.75,1.56-1.8,2.77-3.14,3.62-1.34,.85-2.89,1.27-4.64,1.27-1.42,0-2.7-.3-3.82-.9-1.12-.6-1.99-1.44-2.59-2.51v8.38c0,.71-.23,1.27-.7,1.68-.47,.41-1.08,.62-1.85,.62s-1.39-.22-1.87-.66c-.48-.44-.72-1.01-.72-1.73v-23.29c0-.74,.23-1.33,.68-1.77,.45-.44,1.06-.66,1.83-.66s1.38,.22,1.85,.66c.47,.44,.7,1.03,.7,1.77v1.03c.6-1.12,1.47-1.99,2.61-2.61,1.14-.62,2.43-.92,3.88-.92,1.73,0,3.27,.44,4.62,1.31Zm-2.2,13.95c.9-1.11,1.36-2.68,1.36-4.7s-.45-3.7-1.36-4.85c-.9-1.15-2.16-1.73-3.78-1.73s-2.91,.56-3.8,1.68c-.89,1.12-1.33,2.73-1.33,4.81s.44,3.64,1.33,4.77c.89,1.12,2.16,1.68,3.8,1.68s2.88-.55,3.78-1.66Z"></path><path class="cls-2" d="M219.18,35.15c1.36,.88,2.41,2.12,3.16,3.72,.75,1.6,1.13,3.44,1.13,5.53s-.38,3.9-1.13,5.46c-.75,1.56-1.8,2.77-3.14,3.62-1.34,.85-2.89,1.27-4.64,1.27-1.42,0-2.7-.3-3.82-.9-1.12-.6-1.99-1.44-2.59-2.51v8.38c0,.71-.23,1.27-.7,1.68-.47,.41-1.08,.62-1.85,.62s-1.39-.22-1.87-.66c-.48-.44-.72-1.01-.72-1.73v-23.29c0-.74,.23-1.33,.68-1.77,.45-.44,1.06-.66,1.83-.66s1.38,.22,1.85,.66c.47,.44,.7,1.03,.7,1.77v1.03c.6-1.12,1.47-1.99,2.61-2.61,1.14-.62,2.43-.92,3.88-.92,1.73,0,3.27,.44,4.62,1.31Zm-2.2,13.95c.9-1.11,1.36-2.68,1.36-4.7s-.45-3.7-1.36-4.85c-.9-1.15-2.16-1.73-3.78-1.73s-2.91,.56-3.8,1.68c-.89,1.12-1.33,2.73-1.33,4.81s.44,3.64,1.33,4.77c.89,1.12,2.16,1.68,3.8,1.68s2.88-.55,3.78-1.66Z"></path><path class="cls-2" d="M231.46,53.47c-1.55-.85-2.75-2.07-3.59-3.66-.85-1.59-1.27-3.44-1.27-5.55s.42-3.95,1.27-5.52c.85-1.58,2.05-2.79,3.59-3.64,1.55-.85,3.35-1.27,5.4-1.27s3.85,.42,5.4,1.27,2.74,2.06,3.57,3.64c.83,1.58,1.25,3.42,1.25,5.52s-.42,3.96-1.25,5.55c-.84,1.59-2.03,2.81-3.57,3.66-1.55,.85-3.35,1.27-5.4,1.27s-3.85-.42-5.4-1.27Zm9.18-4.35c.88-1.09,1.31-2.71,1.31-4.85s-.44-3.72-1.31-4.83c-.88-1.11-2.14-1.66-3.78-1.66s-2.91,.55-3.8,1.66c-.89,1.11-1.33,2.72-1.33,4.83s.44,3.75,1.31,4.85c.88,1.1,2.14,1.64,3.78,1.64s2.94-.55,3.82-1.64Z"></path><path class="cls-2" d="M264.22,34.37c.41,.36,.62,.89,.62,1.6s-.18,1.29-.53,1.64c-.36,.36-1,.59-1.93,.7l-1.23,.12c-1.62,.16-2.8,.71-3.55,1.64-.75,.93-1.13,2.09-1.13,3.49v8.63c0,.79-.25,1.4-.74,1.83-.49,.42-1.11,.64-1.85,.64s-1.35-.21-1.83-.64c-.48-.42-.72-1.03-.72-1.83v-15.9c0-.77,.24-1.36,.72-1.77,.48-.41,1.07-.62,1.79-.62s1.29,.2,1.73,.6c.44,.4,.66,.96,.66,1.7v1.64c.52-1.21,1.29-2.14,2.32-2.79,1.03-.66,2.17-1.03,3.43-1.11l.58-.04c.71-.05,1.27,.1,1.68,.45Z"></path><path class="cls-2" d="M281.39,52.69c0,.66-.27,1.16-.8,1.5-.53,.34-1.29,.49-2.28,.43l-1.11-.08c-4.6-.33-6.9-2.79-6.9-7.39v-8.96h-2.05c-.74,0-1.31-.17-1.7-.49-.4-.33-.6-.81-.6-1.44s.2-1.11,.6-1.44c.4-.33,.97-.49,1.7-.49h2.05v-3.78c0-.74,.23-1.33,.7-1.77,.47-.44,1.1-.66,1.89-.66s1.38,.22,1.85,.66c.47,.44,.7,1.03,.7,1.77v3.78h3.49c.74,0,1.31,.17,1.7,.49,.4,.33,.6,.81,.6,1.44s-.2,1.11-.6,1.44c-.4,.33-.97,.49-1.7,.49h-3.49v9.33c0,2.03,.93,3.11,2.79,3.25l1.11,.08c1.37,.08,2.05,.7,2.05,1.85Z"></path><path class="cls-2" d="M324.1,35.79c1.16,1.3,1.75,3.4,1.75,6.31v10.11c0,.79-.24,1.4-.72,1.83-.48,.42-1.1,.64-1.87,.64s-1.35-.21-1.83-.64c-.48-.42-.72-1.03-.72-1.83v-10.11c0-1.51-.27-2.58-.8-3.22s-1.35-.97-2.44-.97c-1.34,0-2.41,.46-3.2,1.38-.79,.92-1.19,2.17-1.19,3.76v9.16c0,.79-.24,1.4-.72,1.83-.48,.42-1.09,.64-1.83,.64s-1.36-.21-1.85-.64c-.49-.42-.74-1.03-.74-1.83v-10.11c0-1.51-.27-2.58-.8-3.22s-1.35-.97-2.44-.97c-1.34,0-2.4,.46-3.18,1.38-.78,.92-1.17,2.17-1.17,3.76v9.16c0,.79-.25,1.4-.74,1.83-.49,.42-1.11,.64-1.85,.64s-1.35-.21-1.83-.64c-.48-.42-.72-1.03-.72-1.83v-15.9c0-.77,.25-1.36,.74-1.77,.49-.41,1.11-.62,1.85-.62s1.29,.2,1.75,.6c.45,.4,.68,.96,.68,1.7v.99c.63-1.09,1.47-1.93,2.53-2.51,1.05-.58,2.27-.86,3.64-.86,3.01,0,5.02,1.26,6.04,3.78,.6-1.15,1.49-2.07,2.67-2.75,1.18-.68,2.51-1.03,3.98-1.03,2.19,0,3.87,.65,5.03,1.95Z"></path><path class="cls-2" d="M348.09,49.45c.29,.36,.43,.84,.43,1.44,0,.85-.51,1.56-1.52,2.14-.93,.52-1.99,.94-3.16,1.25-1.18,.31-2.3,.47-3.37,.47-3.23,0-5.79-.93-7.68-2.79s-2.83-4.41-2.83-7.64c0-2.05,.41-3.88,1.23-5.46s1.98-2.82,3.47-3.7c1.49-.88,3.18-1.31,5.07-1.31s3.38,.4,4.72,1.19c1.34,.79,2.38,1.92,3.12,3.37,.74,1.45,1.11,3.16,1.11,5.13,0,1.18-.52,1.77-1.56,1.77h-12.12c.16,1.89,.7,3.28,1.6,4.17,.9,.89,2.22,1.33,3.94,1.33,.88,0,1.65-.11,2.32-.33,.67-.22,1.43-.52,2.28-.9,.82-.44,1.42-.66,1.81-.66,.47,0,.84,.18,1.13,.53Zm-11.58-10.68c-.84,.88-1.33,2.14-1.5,3.78h9.28c-.06-1.67-.47-2.94-1.23-3.8-.77-.86-1.83-1.29-3.2-1.29s-2.51,.44-3.35,1.32Z"></path><path class="cls-2" d="M367.76,53.47c-1.55-.85-2.75-2.07-3.59-3.66-.85-1.59-1.27-3.44-1.27-5.55s.42-3.95,1.27-5.52c.85-1.58,2.05-2.79,3.59-3.64,1.55-.85,3.35-1.27,5.4-1.27s3.85,.42,5.4,1.27,2.74,2.06,3.57,3.64c.83,1.58,1.25,3.42,1.25,5.52s-.42,3.96-1.25,5.55c-.84,1.59-2.03,2.81-3.57,3.66-1.55,.85-3.35,1.27-5.4,1.27s-3.85-.42-5.4-1.27Zm9.18-4.35c.88-1.09,1.31-2.71,1.31-4.85s-.44-3.72-1.31-4.83c-.88-1.11-2.14-1.66-3.78-1.66s-2.91,.55-3.8,1.66c-.89,1.11-1.33,2.72-1.33,4.83s.44,3.75,1.31,4.85c.88,1.1,2.14,1.64,3.78,1.64s2.94-.55,3.82-1.64Z"></path><path class="cls-2" d="M404.67,35.89c1.18,1.37,1.77,3.44,1.77,6.2v10.11c0,.77-.23,1.37-.68,1.81-.45,.44-1.08,.66-1.87,.66s-1.42-.22-1.89-.66c-.47-.44-.7-1.04-.7-1.81v-9.82c0-1.56-.29-2.7-.88-3.41-.59-.71-1.51-1.07-2.77-1.07-1.48,0-2.66,.47-3.55,1.4-.89,.93-1.33,2.18-1.33,3.74v9.16c0,.77-.23,1.37-.7,1.81-.47,.44-1.1,.66-1.89,.66s-1.42-.22-1.87-.66c-.45-.44-.68-1.04-.68-1.81v-15.9c0-.71,.23-1.29,.7-1.73,.47-.44,1.09-.66,1.89-.66,.71,0,1.29,.21,1.75,.64,.45,.42,.68,.98,.68,1.66v1.11c.68-1.12,1.6-1.99,2.75-2.59,1.15-.6,2.45-.9,3.9-.9,2.41,0,4.2,.68,5.38,2.05Z"></path><path class="cls-2" d="M446.54,52.2c0,.63-.24,1.19-.72,1.66-.48,.48-1.05,.72-1.7,.72s-1.32-.29-1.89-.86l-13.68-12.82v11.09c0,.79-.24,1.44-.72,1.93s-1.13,.74-1.95,.74-1.43-.25-1.91-.74-.72-1.14-.72-1.93V27.88c0-.79,.24-1.43,.72-1.91,.48-.48,1.12-.72,1.91-.72s1.47,.24,1.95,.72c.48,.48,.72,1.12,.72,1.91v10.56l13.23-12.41c.49-.49,1.05-.74,1.68-.74s1.18,.24,1.66,.72c.48,.48,.72,1.03,.72,1.66s-.27,1.21-.82,1.73l-11.05,10.11,11.75,10.93c.55,.52,.82,1.11,.82,1.77Z"></path><path class="cls-2" d="M453.46,53.47c-1.55-.85-2.75-2.07-3.59-3.66-.85-1.59-1.27-3.44-1.27-5.55s.42-3.95,1.27-5.52c.85-1.58,2.05-2.79,3.59-3.64s3.35-1.27,5.4-1.27,3.85,.42,5.4,1.27c1.55,.85,2.74,2.06,3.57,3.64,.84,1.58,1.25,3.42,1.25,5.52s-.42,3.96-1.25,5.55c-.83,1.59-2.03,2.81-3.57,3.66-1.55,.85-3.35,1.27-5.4,1.27s-3.86-.42-5.4-1.27Zm9.18-4.35c.88-1.09,1.31-2.71,1.31-4.85s-.44-3.72-1.31-4.83c-.88-1.11-2.14-1.66-3.78-1.66s-2.91,.55-3.8,1.66c-.89,1.11-1.33,2.72-1.33,4.83s.44,3.75,1.31,4.85c.88,1.1,2.14,1.64,3.78,1.64s2.94-.55,3.82-1.64Z"></path><path class="cls-2" d="M474.16,44.93c-.4-.36-.6-.85-.6-1.48s.2-1.13,.6-1.5c.4-.37,.99-.55,1.79-.55h8.46c.79,0,1.39,.18,1.79,.55,.4,.37,.6,.87,.6,1.5s-.2,1.12-.6,1.48c-.4,.36-.99,.53-1.79,.53h-8.46c-.79,0-1.39-.18-1.79-.53Z"></path><path class="cls-2" d="M511.57,36.26v15.94c0,.79-.24,1.4-.72,1.83-.48,.42-1.09,.64-1.83,.64s-1.36-.21-1.85-.64c-.49-.42-.74-1.03-.74-1.83v-14.01h-8.13v14.01c0,.79-.24,1.4-.72,1.81-.48,.41-1.09,.62-1.83,.62s-1.36-.21-1.85-.62-.74-1.01-.74-1.81v-14.01h-2.05c-1.53,0-2.3-.64-2.3-1.93s.77-1.93,2.3-1.93h2.05v-.21c0-2.55,.68-4.57,2.03-6.06,1.36-1.49,3.27-2.35,5.73-2.57l.95-.08c.14-.03,.34-.04,.62-.04,1.45,0,2.18,.62,2.18,1.85,0,.6-.16,1.07-.49,1.4-.33,.33-.78,.52-1.36,.58l-.9,.08c-1.26,.11-2.18,.5-2.75,1.17-.58,.67-.86,1.65-.86,2.94v.94h10.97c1.53,0,2.3,.64,2.3,1.93Zm-4.77-6.76c-.55-.51-.82-1.17-.82-1.99s.27-1.48,.82-1.97,1.29-.74,2.22-.74,1.64,.25,2.2,.74c.56,.49,.84,1.15,.84,1.97s-.28,1.49-.84,1.99c-.56,.51-1.29,.76-2.2,.76s-1.67-.25-2.22-.76Z"></path></g></svg></li></ul></div></div></div></footer><template id="tp-language" data-tp-language="en_US"></template>        <div id="trp-floater-ls" onclick="" data-no-translation class="trp-language-switcher-container trp-floater-ls-names trp-bottom-right trp-color-dark flags-full-names" >
            <div id="trp-floater-ls-current-language" class="trp-with-flags">

                <a href="#" class="trp-floater-ls-disabled-language trp-ls-disabled-language" onclick="event.preventDefault()">
					<img class="trp-flag-image lazyload" data-src="https://aachips.co/wp-content/plugins/translatepress-multilingual/assets/images/flags/en_US.png" width="18" height="12" alt="en_US" title="English" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==" style="--smush-placeholder-width: 18px; --smush-placeholder-aspect-ratio: 18/12;"><noscript><img class="trp-flag-image lazyload" data-src="https://aachips.co/wp-content/plugins/translatepress-multilingual/assets/images/flags/en_US.png" width="18" height="12" alt="en_US" title="English" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==" style="--smush-placeholder-width: 18px; --smush-placeholder-aspect-ratio: 18/12;"><noscript><img class="trp-flag-image" src="https://aachips.co/wp-content/plugins/translatepress-multilingual/assets/images/flags/en_US.png" width="18" height="12" alt="en_US" title="English"></noscript></noscript>English				</a>

            </div>
            <div id="trp-floater-ls-language-list" class="trp-with-flags" >

                <div class="trp-language-wrap trp-language-wrap-bottom"><a href="#" class="trp-floater-ls-disabled-language trp-ls-disabled-language" onclick="event.preventDefault()"><img class="trp-flag-image lazyload" data-src="https://aachips.co/wp-content/plugins/translatepress-multilingual/assets/images/flags/en_US.png" width="18" height="12" alt="en_US" title="English" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==" style="--smush-placeholder-width: 18px; --smush-placeholder-aspect-ratio: 18/12;"><noscript><img class="trp-flag-image lazyload" data-src="https://aachips.co/wp-content/plugins/translatepress-multilingual/assets/images/flags/en_US.png" width="18" height="12" alt="en_US" title="English" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==" style="--smush-placeholder-width: 18px; --smush-placeholder-aspect-ratio: 18/12;"><noscript><img class="trp-flag-image" src="https://aachips.co/wp-content/plugins/translatepress-multilingual/assets/images/flags/en_US.png" width="18" height="12" alt="en_US" title="English"></noscript></noscript>English</a></div>            </div>
        </div>

    <link rel='stylesheet' id='wp-block-library-css' href='https://aachips.co/wp-includes/css/dist/block-library/style.min.css?ver=6.5.5' media='all' />
<style id='global-styles-inline-css'>
body{--wp--preset--color--black: #000000;--wp--preset--color--cyan-bluish-gray: #abb8c3;--wp--preset--color--white: #ffffff;--wp--preset--color--pale-pink: #f78da7;--wp--preset--color--vivid-red: #cf2e2e;--wp--preset--color--luminous-vivid-orange: #ff6900;--wp--preset--color--luminous-vivid-amber: #fcb900;--wp--preset--color--light-green-cyan: #7bdcb5;--wp--preset--color--vivid-green-cyan: #00d084;--wp--preset--color--pale-cyan-blue: #8ed1fc;--wp--preset--color--vivid-cyan-blue: #0693e3;--wp--preset--color--vivid-purple: #9b51e0;--wp--preset--gradient--vivid-cyan-blue-to-vivid-purple: linear-gradient(135deg,rgba(6,147,227,1) 0%,rgb(155,81,224) 100%);--wp--preset--gradient--light-green-cyan-to-vivid-green-cyan: linear-gradient(135deg,rgb(122,220,180) 0%,rgb(0,208,130) 100%);--wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange: linear-gradient(135deg,rgba(252,185,0,1) 0%,rgba(255,105,0,1) 100%);--wp--preset--gradient--luminous-vivid-orange-to-vivid-red: linear-gradient(135deg,rgba(255,105,0,1) 0%,rgb(207,46,46) 100%);--wp--preset--gradient--very-light-gray-to-cyan-bluish-gray: linear-gradient(135deg,rgb(238,238,238) 0%,rgb(169,184,195) 100%);--wp--preset--gradient--cool-to-warm-spectrum: linear-gradient(135deg,rgb(74,234,220) 0%,rgb(151,120,209) 20%,rgb(207,42,186) 40%,rgb(238,44,130) 60%,rgb(251,105,98) 80%,rgb(254,248,76) 100%);--wp--preset--gradient--blush-light-purple: linear-gradient(135deg,rgb(255,206,236) 0%,rgb(152,150,240) 100%);--wp--preset--gradient--blush-bordeaux: linear-gradient(135deg,rgb(254,205,165) 0%,rgb(254,45,45) 50%,rgb(107,0,62) 100%);--wp--preset--gradient--luminous-dusk: linear-gradient(135deg,rgb(255,203,112) 0%,rgb(199,81,192) 50%,rgb(65,88,208) 100%);--wp--preset--gradient--pale-ocean: linear-gradient(135deg,rgb(255,245,203) 0%,rgb(182,227,212) 50%,rgb(51,167,181) 100%);--wp--preset--gradient--electric-grass: linear-gradient(135deg,rgb(202,248,128) 0%,rgb(113,206,126) 100%);--wp--preset--gradient--midnight: linear-gradient(135deg,rgb(2,3,129) 0%,rgb(40,116,252) 100%);--wp--preset--font-size--small: 13px;--wp--preset--font-size--medium: 20px;--wp--preset--font-size--large: 36px;--wp--preset--font-size--x-large: 42px;--wp--preset--spacing--20: 0.44rem;--wp--preset--spacing--30: 0.67rem;--wp--preset--spacing--40: 1rem;--wp--preset--spacing--50: 1.5rem;--wp--preset--spacing--60: 2.25rem;--wp--preset--spacing--70: 3.38rem;--wp--preset--spacing--80: 5.06rem;--wp--preset--shadow--natural: 6px 6px 9px rgba(0, 0, 0, 0.2);--wp--preset--shadow--deep: 12px 12px 50px rgba(0, 0, 0, 0.4);--wp--preset--shadow--sharp: 6px 6px 0px rgba(0, 0, 0, 0.2);--wp--preset--shadow--outlined: 6px 6px 0px -3px rgba(255, 255, 255, 1), 6px 6px rgba(0, 0, 0, 1);--wp--preset--shadow--crisp: 6px 6px 0px rgba(0, 0, 0, 1);}:where(.is-layout-flex){gap: 0.5em;}:where(.is-layout-grid){gap: 0.5em;}body .is-layout-flex{display: flex;}body .is-layout-flex{flex-wrap: wrap;align-items: center;}body .is-layout-flex > *{margin: 0;}body .is-layout-grid{display: grid;}body .is-layout-grid > *{margin: 0;}:where(.wp-block-columns.is-layout-flex){gap: 2em;}:where(.wp-block-columns.is-layout-grid){gap: 2em;}:where(.wp-block-post-template.is-layout-flex){gap: 1.25em;}:where(.wp-block-post-template.is-layout-grid){gap: 1.25em;}.has-black-color{color: var(--wp--preset--color--black) !important;}.has-cyan-bluish-gray-color{color: var(--wp--preset--color--cyan-bluish-gray) !important;}.has-white-color{color: var(--wp--preset--color--white) !important;}.has-pale-pink-color{color: var(--wp--preset--color--pale-pink) !important;}.has-vivid-red-color{color: var(--wp--preset--color--vivid-red) !important;}.has-luminous-vivid-orange-color{color: var(--wp--preset--color--luminous-vivid-orange) !important;}.has-luminous-vivid-amber-color{color: var(--wp--preset--color--luminous-vivid-amber) !important;}.has-light-green-cyan-color{color: var(--wp--preset--color--light-green-cyan) !important;}.has-vivid-green-cyan-color{color: var(--wp--preset--color--vivid-green-cyan) !important;}.has-pale-cyan-blue-color{color: var(--wp--preset--color--pale-cyan-blue) !important;}.has-vivid-cyan-blue-color{color: var(--wp--preset--color--vivid-cyan-blue) !important;}.has-vivid-purple-color{color: var(--wp--preset--color--vivid-purple) !important;}.has-black-background-color{background-color: var(--wp--preset--color--black) !important;}.has-cyan-bluish-gray-background-color{background-color: var(--wp--preset--color--cyan-bluish-gray) !important;}.has-white-background-color{background-color: var(--wp--preset--color--white) !important;}.has-pale-pink-background-color{background-color: var(--wp--preset--color--pale-pink) !important;}.has-vivid-red-background-color{background-color: var(--wp--preset--color--vivid-red) !important;}.has-luminous-vivid-orange-background-color{background-color: var(--wp--preset--color--luminous-vivid-orange) !important;}.has-luminous-vivid-amber-background-color{background-color: var(--wp--preset--color--luminous-vivid-amber) !important;}.has-light-green-cyan-background-color{background-color: var(--wp--preset--color--light-green-cyan) !important;}.has-vivid-green-cyan-background-color{background-color: var(--wp--preset--color--vivid-green-cyan) !important;}.has-pale-cyan-blue-background-color{background-color: var(--wp--preset--color--pale-cyan-blue) !important;}.has-vivid-cyan-blue-background-color{background-color: var(--wp--preset--color--vivid-cyan-blue) !important;}.has-vivid-purple-background-color{background-color: var(--wp--preset--color--vivid-purple) !important;}.has-black-border-color{border-color: var(--wp--preset--color--black) !important;}.has-cyan-bluish-gray-border-color{border-color: var(--wp--preset--color--cyan-bluish-gray) !important;}.has-white-border-color{border-color: var(--wp--preset--color--white) !important;}.has-pale-pink-border-color{border-color: var(--wp--preset--color--pale-pink) !important;}.has-vivid-red-border-color{border-color: var(--wp--preset--color--vivid-red) !important;}.has-luminous-vivid-orange-border-color{border-color: var(--wp--preset--color--luminous-vivid-orange) !important;}.has-luminous-vivid-amber-border-color{border-color: var(--wp--preset--color--luminous-vivid-amber) !important;}.has-light-green-cyan-border-color{border-color: var(--wp--preset--color--light-green-cyan) !important;}.has-vivid-green-cyan-border-color{border-color: var(--wp--preset--color--vivid-green-cyan) !important;}.has-pale-cyan-blue-border-color{border-color: var(--wp--preset--color--pale-cyan-blue) !important;}.has-vivid-cyan-blue-border-color{border-color: var(--wp--preset--color--vivid-cyan-blue) !important;}.has-vivid-purple-border-color{border-color: var(--wp--preset--color--vivid-purple) !important;}.has-vivid-cyan-blue-to-vivid-purple-gradient-background{background: var(--wp--preset--gradient--vivid-cyan-blue-to-vivid-purple) !important;}.has-light-green-cyan-to-vivid-green-cyan-gradient-background{background: var(--wp--preset--gradient--light-green-cyan-to-vivid-green-cyan) !important;}.has-luminous-vivid-amber-to-luminous-vivid-orange-gradient-background{background: var(--wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange) !important;}.has-luminous-vivid-orange-to-vivid-red-gradient-background{background: var(--wp--preset--gradient--luminous-vivid-orange-to-vivid-red) !important;}.has-very-light-gray-to-cyan-bluish-gray-gradient-background{background: var(--wp--preset--gradient--very-light-gray-to-cyan-bluish-gray) !important;}.has-cool-to-warm-spectrum-gradient-background{background: var(--wp--preset--gradient--cool-to-warm-spectrum) !important;}.has-blush-light-purple-gradient-background{background: var(--wp--preset--gradient--blush-light-purple) !important;}.has-blush-bordeaux-gradient-background{background: var(--wp--preset--gradient--blush-bordeaux) !important;}.has-luminous-dusk-gradient-background{background: var(--wp--preset--gradient--luminous-dusk) !important;}.has-pale-ocean-gradient-background{background: var(--wp--preset--gradient--pale-ocean) !important;}.has-electric-grass-gradient-background{background: var(--wp--preset--gradient--electric-grass) !important;}.has-midnight-gradient-background{background: var(--wp--preset--gradient--midnight) !important;}.has-small-font-size{font-size: var(--wp--preset--font-size--small) !important;}.has-medium-font-size{font-size: var(--wp--preset--font-size--medium) !important;}.has-large-font-size{font-size: var(--wp--preset--font-size--large) !important;}.has-x-large-font-size{font-size: var(--wp--preset--font-size--x-large) !important;}
.wp-block-navigation a:where(:not(.wp-element-button)){color: inherit;}
:where(.wp-block-post-template.is-layout-flex){gap: 1.25em;}:where(.wp-block-post-template.is-layout-grid){gap: 1.25em;}
:where(.wp-block-columns.is-layout-flex){gap: 2em;}:where(.wp-block-columns.is-layout-grid){gap: 2em;}
.wp-block-pullquote{font-size: 1.5em;line-height: 1.6;}
</style>
<script id="chatway-script-js-extra">
var wpChatwaySettings = {"widgetId":"QGxjxq7wNQi8"};
</script>
<script src="https://cdn.chatway.app/widget.js?id=QGxjxq7wNQi8&amp;ver=f4821a1bc1e546a12f05" id="chatway-script-js"></script>
<script id="bricks-scripts-js-extra">
var bricksData = {"debug":"","locale":"en_US","ajaxUrl":"https:\/\/aachips.co\/wp-admin\/admin-ajax.php","restApiUrl":"https:\/\/aachips.co\/wp-json\/bricks\/v1\/","nonce":"da1260884a","formNonce":"4c8f2deb2f","wpRestNonce":"c474f32a91","postId":"745","recaptchaIds":[],"animatedTypingInstances":[],"videoInstances":[],"splideInstances":[],"tocbotInstances":[],"swiperInstances":[],"queryLoopInstances":[],"interactions":[],"filterInstances":[],"isotopeInstances":[],"mapStyles":{"ultraLightWithLabels":{"label":"Ultra light with labels","style":"[ { \"featureType\": \"water\", \"elementType\": \"geometry\", \"stylers\": [ { \"color\": \"#e9e9e9\" }, { \"lightness\": 17 } ] }, { \"featureType\": \"landscape\", \"elementType\": \"geometry\", \"stylers\": [ { \"color\": \"#f5f5f5\" }, { \"lightness\": 20 } ] }, { \"featureType\": \"road.highway\", \"elementType\": \"geometry.fill\", \"stylers\": [ { \"color\": \"#ffffff\" }, { \"lightness\": 17 } ] }, { \"featureType\": \"road.highway\", \"elementType\": \"geometry.stroke\", \"stylers\": [ { \"color\": \"#ffffff\" }, { \"lightness\": 29 }, { \"weight\": 0.2 } ] }, { \"featureType\": \"road.arterial\", \"elementType\": \"geometry\", \"stylers\": [ { \"color\": \"#ffffff\" }, { \"lightness\": 18 } ] }, { \"featureType\": \"road.local\", \"elementType\": \"geometry\", \"stylers\": [ { \"color\": \"#ffffff\" }, { \"lightness\": 16 } ] }, { \"featureType\": \"poi\", \"elementType\": \"geometry\", \"stylers\": [ { \"color\": \"#f5f5f5\" }, { \"lightness\": 21 } ] }, { \"featureType\": \"poi.park\", \"elementType\": \"geometry\", \"stylers\": [ { \"color\": \"#dedede\" }, { \"lightness\": 21 } ] }, { \"elementType\": \"labels.text.stroke\", \"stylers\": [ { \"visibility\": \"on\" }, { \"color\": \"#ffffff\" }, { \"lightness\": 16 } ] }, { \"elementType\": \"labels.text.fill\", \"stylers\": [ { \"saturation\": 36 }, { \"color\": \"#333333\" }, { \"lightness\": 40 } ] }, { \"elementType\": \"labels.icon\", \"stylers\": [ { \"visibility\": \"off\" } ] }, { \"featureType\": \"transit\", \"elementType\": \"geometry\", \"stylers\": [ { \"color\": \"#f2f2f2\" }, { \"lightness\": 19 } ] }, { \"featureType\": \"administrative\", \"elementType\": \"geometry.fill\", \"stylers\": [ { \"color\": \"#fefefe\" }, { \"lightness\": 20 } ] }, { \"featureType\": \"administrative\", \"elementType\": \"geometry.stroke\", \"stylers\": [ { \"color\": \"#fefefe\" }, { \"lightness\": 17 }, { \"weight\": 1.2 } ] } ]"},"blueWater":{"label":"Blue water","style":"[ { \"featureType\": \"administrative\", \"elementType\": \"labels.text.fill\", \"stylers\": [ { \"color\": \"#444444\" } ] }, { \"featureType\": \"landscape\", \"elementType\": \"all\", \"stylers\": [ { \"color\": \"#f2f2f2\" } ] }, { \"featureType\": \"poi\", \"elementType\": \"all\", \"stylers\": [ { \"visibility\": \"off\" } ] }, { \"featureType\": \"road\", \"elementType\": \"all\", \"stylers\": [ { \"saturation\": -100 }, { \"lightness\": 45 } ] }, { \"featureType\": \"road.highway\", \"elementType\": \"all\", \"stylers\": [ { \"visibility\": \"simplified\" } ] }, { \"featureType\": \"road.arterial\", \"elementType\": \"labels.icon\", \"stylers\": [ { \"visibility\": \"off\" } ] }, { \"featureType\": \"transit\", \"elementType\": \"all\", \"stylers\": [ { \"visibility\": \"off\" } ] }, { \"featureType\": \"water\", \"elementType\": \"all\", \"stylers\": [ { \"color\": \"#46bcec\" }, { \"visibility\": \"on\" } ] } ]"},"lightDream":{"label":"Light dream","style":"[ { \"featureType\": \"landscape\", \"stylers\": [ { \"hue\": \"#FFBB00\" }, { \"saturation\": 43.400000000000006 }, { \"lightness\": 37.599999999999994 }, { \"gamma\": 1 } ] }, { \"featureType\": \"road.highway\", \"stylers\": [ { \"hue\": \"#FFC200\" }, { \"saturation\": -61.8 }, { \"lightness\": 45.599999999999994 }, { \"gamma\": 1 } ] }, { \"featureType\": \"road.arterial\", \"stylers\": [ { \"hue\": \"#FF0300\" }, { \"saturation\": -100 }, { \"lightness\": 51.19999999999999 }, { \"gamma\": 1 } ] }, { \"featureType\": \"road.local\", \"stylers\": [ { \"hue\": \"#FF0300\" }, { \"saturation\": -100 }, { \"lightness\": 52 }, { \"gamma\": 1 } ] }, { \"featureType\": \"water\", \"stylers\": [ { \"hue\": \"#0078FF\" }, { \"saturation\": -13.200000000000003 }, { \"lightness\": 2.4000000000000057 }, { \"gamma\": 1 } ] }, { \"featureType\": \"poi\", \"stylers\": [ { \"hue\": \"#00FF6A\" }, { \"saturation\": -1.0989010989011234 }, { \"lightness\": 11.200000000000017 }, { \"gamma\": 1 } ] } ]"},"blueEssence":{"label":"Blue essence","style":"[ { \"featureType\": \"landscape.natural\", \"elementType\": \"geometry.fill\", \"stylers\": [ { \"visibility\": \"on\" }, { \"color\": \"#e0efef\" } ] }, { \"featureType\": \"poi\", \"elementType\": \"geometry.fill\", \"stylers\": [ { \"visibility\": \"on\" }, { \"hue\": \"#1900ff\" }, { \"color\": \"#c0e8e8\" } ] }, { \"featureType\": \"road\", \"elementType\": \"geometry\", \"stylers\": [ { \"lightness\": 100 }, { \"visibility\": \"simplified\" } ] }, { \"featureType\": \"road\", \"elementType\": \"labels\", \"stylers\": [ { \"visibility\": \"off\" } ] }, { \"featureType\": \"transit.line\", \"elementType\": \"geometry\", \"stylers\": [ { \"visibility\": \"on\" }, { \"lightness\": 700 } ] }, { \"featureType\": \"water\", \"elementType\": \"all\", \"stylers\": [ { \"color\": \"#7dcdcd\" } ] } ]"},"appleMapsesque":{"label":"Apple maps-esque","style":"[ { \"featureType\": \"landscape.man_made\", \"elementType\": \"geometry\", \"stylers\": [ { \"color\": \"#f7f1df\" } ] }, { \"featureType\": \"landscape.natural\", \"elementType\": \"geometry\", \"stylers\": [ { \"color\": \"#d0e3b4\" } ] }, { \"featureType\": \"landscape.natural.terrain\", \"elementType\": \"geometry\", \"stylers\": [ { \"visibility\": \"off\" } ] }, { \"featureType\": \"poi\", \"elementType\": \"labels\", \"stylers\": [ { \"visibility\": \"off\" } ] }, { \"featureType\": \"poi.business\", \"elementType\": \"all\", \"stylers\": [ { \"visibility\": \"off\" } ] }, { \"featureType\": \"poi.medical\", \"elementType\": \"geometry\", \"stylers\": [ { \"color\": \"#fbd3da\" } ] }, { \"featureType\": \"poi.park\", \"elementType\": \"geometry\", \"stylers\": [ { \"color\": \"#bde6ab\" } ] }, { \"featureType\": \"road\", \"elementType\": \"geometry.stroke\", \"stylers\": [ { \"visibility\": \"off\" } ] }, { \"featureType\": \"road\", \"elementType\": \"labels\", \"stylers\": [ { \"visibility\": \"off\" } ] }, { \"featureType\": \"road.highway\", \"elementType\": \"geometry.fill\", \"stylers\": [ { \"color\": \"#ffe15f\" } ] }, { \"featureType\": \"road.highway\", \"elementType\": \"geometry.stroke\", \"stylers\": [ { \"color\": \"#efd151\" } ] }, { \"featureType\": \"road.arterial\", \"elementType\": \"geometry.fill\", \"stylers\": [ { \"color\": \"#ffffff\" } ] }, { \"featureType\": \"road.local\", \"elementType\": \"geometry.fill\", \"stylers\": [ { \"color\": \"black\" } ] }, { \"featureType\": \"transit.station.airport\", \"elementType\": \"geometry.fill\", \"stylers\": [ { \"color\": \"#cfb2db\" } ] }, { \"featureType\": \"water\", \"elementType\": \"geometry\", \"stylers\": [ { \"color\": \"#a2daf2\" } ] } ]"},"paleDawn":{"label":"Pale dawn","style":"[ { \"featureType\": \"administrative\", \"elementType\": \"all\", \"stylers\": [ { \"visibility\": \"on\" }, { \"lightness\": 33 } ] }, { \"featureType\": \"landscape\", \"elementType\": \"all\", \"stylers\": [ { \"color\": \"#f2e5d4\" } ] }, { \"featureType\": \"poi.park\", \"elementType\": \"geometry\", \"stylers\": [ { \"color\": \"#c5dac6\" } ] }, { \"featureType\": \"poi.park\", \"elementType\": \"labels\", \"stylers\": [ { \"visibility\": \"on\" }, { \"lightness\": 20 } ] }, { \"featureType\": \"road\", \"elementType\": \"all\", \"stylers\": [ { \"lightness\": 20 } ] }, { \"featureType\": \"road.highway\", \"elementType\": \"geometry\", \"stylers\": [ { \"color\": \"#c5c6c6\" } ] }, { \"featureType\": \"road.arterial\", \"elementType\": \"geometry\", \"stylers\": [ { \"color\": \"#e4d7c6\" } ] }, { \"featureType\": \"road.local\", \"elementType\": \"geometry\", \"stylers\": [ { \"color\": \"#fbfaf7\" } ] }, { \"featureType\": \"water\", \"elementType\": \"all\", \"stylers\": [ { \"visibility\": \"on\" }, { \"color\": \"#acbcc9\" } ] } ]"},"neutralBlue":{"label":"Neutral blue","style":"[ { \"featureType\": \"water\", \"elementType\": \"geometry\", \"stylers\": [ { \"color\": \"#193341\" } ] }, { \"featureType\": \"landscape\", \"elementType\": \"geometry\", \"stylers\": [ { \"color\": \"#2c5a71\" } ] }, { \"featureType\": \"road\", \"elementType\": \"geometry\", \"stylers\": [ { \"color\": \"#29768a\" }, { \"lightness\": -37 } ] }, { \"featureType\": \"poi\", \"elementType\": \"geometry\", \"stylers\": [ { \"color\": \"#406d80\" } ] }, { \"featureType\": \"transit\", \"elementType\": \"geometry\", \"stylers\": [ { \"color\": \"#406d80\" } ] }, { \"elementType\": \"labels.text.stroke\", \"stylers\": [ { \"visibility\": \"on\" }, { \"color\": \"#3e606f\" }, { \"weight\": 2 }, { \"gamma\": 0.84 } ] }, { \"elementType\": \"labels.text.fill\", \"stylers\": [ { \"color\": \"#ffffff\" } ] }, { \"featureType\": \"administrative\", \"elementType\": \"geometry\", \"stylers\": [ { \"weight\": 0.6 }, { \"color\": \"#1a3541\" } ] }, { \"elementType\": \"labels.icon\", \"stylers\": [ { \"visibility\": \"off\" } ] }, { \"featureType\": \"poi.park\", \"elementType\": \"geometry\", \"stylers\": [ { \"color\": \"#2c5a71\" } ] } ]"},"avocadoWorld":{"label":"Avocado world","style":"[ { \"featureType\": \"water\", \"elementType\": \"geometry\", \"stylers\": [ { \"visibility\": \"on\" }, { \"color\": \"#aee2e0\" } ] }, { \"featureType\": \"landscape\", \"elementType\": \"geometry.fill\", \"stylers\": [ { \"color\": \"#abce83\" } ] }, { \"featureType\": \"poi\", \"elementType\": \"geometry.fill\", \"stylers\": [ { \"color\": \"#769E72\" } ] }, { \"featureType\": \"poi\", \"elementType\": \"labels.text.fill\", \"stylers\": [ { \"color\": \"#7B8758\" } ] }, { \"featureType\": \"poi\", \"elementType\": \"labels.text.stroke\", \"stylers\": [ { \"color\": \"#EBF4A4\" } ] }, { \"featureType\": \"poi.park\", \"elementType\": \"geometry\", \"stylers\": [ { \"visibility\": \"simplified\" }, { \"color\": \"#8dab68\" } ] }, { \"featureType\": \"road\", \"elementType\": \"geometry.fill\", \"stylers\": [ { \"visibility\": \"simplified\" } ] }, { \"featureType\": \"road\", \"elementType\": \"labels.text.fill\", \"stylers\": [ { \"color\": \"#5B5B3F\" } ] }, { \"featureType\": \"road\", \"elementType\": \"labels.text.stroke\", \"stylers\": [ { \"color\": \"#ABCE83\" } ] }, { \"featureType\": \"road\", \"elementType\": \"labels.icon\", \"stylers\": [ { \"visibility\": \"off\" } ] }, { \"featureType\": \"road.local\", \"elementType\": \"geometry\", \"stylers\": [ { \"color\": \"#A4C67D\" } ] }, { \"featureType\": \"road.arterial\", \"elementType\": \"geometry\", \"stylers\": [ { \"color\": \"#9BBF72\" } ] }, { \"featureType\": \"road.highway\", \"elementType\": \"geometry\", \"stylers\": [ { \"color\": \"#EBF4A4\" } ] }, { \"featureType\": \"transit\", \"stylers\": [ { \"visibility\": \"off\" } ] }, { \"featureType\": \"administrative\", \"elementType\": \"geometry.stroke\", \"stylers\": [ { \"visibility\": \"on\" }, { \"color\": \"#87ae79\" } ] }, { \"featureType\": \"administrative\", \"elementType\": \"geometry.fill\", \"stylers\": [ { \"color\": \"#7f2200\" }, { \"visibility\": \"off\" } ] }, { \"featureType\": \"administrative\", \"elementType\": \"labels.text.stroke\", \"stylers\": [ { \"color\": \"#ffffff\" }, { \"visibility\": \"on\" }, { \"weight\": 4.1 } ] }, { \"featureType\": \"administrative\", \"elementType\": \"labels.text.fill\", \"stylers\": [ { \"color\": \"#495421\" } ] }, { \"featureType\": \"administrative.neighborhood\", \"elementType\": \"labels\", \"stylers\": [ { \"visibility\": \"off\" } ] } ]"},"gowalla":{"label":"Gowalla","style":"[ { \"featureType\": \"administrative.land_parcel\", \"elementType\": \"all\", \"stylers\": [ { \"visibility\": \"off\" } ] }, { \"featureType\": \"landscape.man_made\", \"elementType\": \"all\", \"stylers\": [ { \"visibility\": \"off\" } ] }, { \"featureType\": \"poi\", \"elementType\": \"labels\", \"stylers\": [ { \"visibility\": \"off\" } ] }, { \"featureType\": \"road\", \"elementType\": \"labels\", \"stylers\": [ { \"visibility\": \"simplified\" }, { \"lightness\": 20 } ] }, { \"featureType\": \"road.highway\", \"elementType\": \"geometry\", \"stylers\": [ { \"hue\": \"#f49935\" } ] }, { \"featureType\": \"road.highway\", \"elementType\": \"labels\", \"stylers\": [ { \"visibility\": \"simplified\" } ] }, { \"featureType\": \"road.arterial\", \"elementType\": \"geometry\", \"stylers\": [ { \"hue\": \"#fad959\" } ] }, { \"featureType\": \"road.arterial\", \"elementType\": \"labels\", \"stylers\": [ { \"visibility\": \"off\" } ] }, { \"featureType\": \"road.local\", \"elementType\": \"geometry\", \"stylers\": [ { \"visibility\": \"simplified\" } ] }, { \"featureType\": \"road.local\", \"elementType\": \"labels\", \"stylers\": [ { \"visibility\": \"simplified\" } ] }, { \"featureType\": \"transit\", \"elementType\": \"all\", \"stylers\": [ { \"visibility\": \"off\" } ] }, { \"featureType\": \"water\", \"elementType\": \"all\", \"stylers\": [ { \"hue\": \"#a1cdfc\" }, { \"saturation\": 30 }, { \"lightness\": 49 } ] } ]"}},"facebookAppId":"","headerPosition":"top","offsetLazyLoad":"300","baseUrl":"https:\/\/aachips.co\/virtual-care-access\/","useQueryFilter":"","pageFilters":[]};
</script>
<script src="https://aachips.co/wp-content/themes/bricks/assets/js/bricks.min.js?ver=1716603503" id="bricks-scripts-js"></script>
<script src="https://aachips.co/wp-content/plugins/wp-smushit/app/assets/js/smush-lazy-load.min.js?ver=3.16.5" id="smush-lazy-load-js"></script>
</body></html>