<?php
/**
 * Database update script to add user_type column
 * Run this script once to update your database for the new user type system
 */

// Include database functions
require_once 'php/includes/db.php';
require_once 'php/includes/functions.php';

// Set page variables
$pageTitle = 'Database Update - User Types';
$pageDescription = 'Update database to support user types';
$currentPage = 'admin';

$message = '';
$messageType = '';

// Process update if requested
if (isset($_POST['update_database'])) {
    $conn = get_db_connection();
    
    if (!$conn) {
        $message = 'Database connection failed.';
        $messageType = 'error';
    } else {
        try {
            // Check if user_type column exists
            $check_column = $conn->query("SHOW COLUMNS FROM users LIKE 'user_type'");
            
            if ($check_column->num_rows == 0) {
                // Add the user_type column
                $alter_sql = "ALTER TABLE users ADD COLUMN user_type ENUM('help_seeker', 'business_organization', 'volunteer_mutual_aid') DEFAULT 'help_seeker' AFTER slug";
                
                if ($conn->query($alter_sql)) {
                    $message = 'Successfully added user_type column to users table!';
                    $messageType = 'success';
                    
                    // Update existing users to have the default user_type
                    $update_sql = "UPDATE users SET user_type = 'help_seeker' WHERE user_type IS NULL";
                    $conn->query($update_sql);
                    
                } else {
                    $message = 'Error adding user_type column: ' . $conn->error;
                    $messageType = 'error';
                }
            } else {
                $message = 'user_type column already exists in the database.';
                $messageType = 'info';
            }
            
        } catch (Exception $e) {
            $message = 'Database error: ' . $e->getMessage();
            $messageType = 'error';
        }
    }
}

// Check current database status
$conn = get_db_connection();
$has_user_type = false;
$user_count = 0;

if ($conn) {
    $check_column = $conn->query("SHOW COLUMNS FROM users LIKE 'user_type'");
    $has_user_type = ($check_column->num_rows > 0);
    
    $count_result = $conn->query("SELECT COUNT(*) as count FROM users");
    if ($count_result) {
        $user_count = $count_result->fetch_assoc()['count'];
    }
}

// Include header
include_once 'templates/components/header.php';
?>

<div class="auth-page">
    <div class="container">
        <div class="auth-card">
            <div class="auth-header">
                <h1>Database Update - User Types</h1>
                <p>Update your database to support the new three-type user registration system</p>
            </div>
            
            <?php if (!empty($message)): ?>
                <div class="alert alert-<?php echo $messageType; ?>">
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>
            
            <div class="database-status">
                <h3>Current Database Status:</h3>
                <ul>
                    <li><strong>Database Connection:</strong> <?php echo $conn ? '✅ Connected' : '❌ Failed'; ?></li>
                    <li><strong>User Type Column:</strong> <?php echo $has_user_type ? '✅ Exists' : '❌ Missing'; ?></li>
                    <li><strong>Total Users:</strong> <?php echo $user_count; ?></li>
                </ul>
            </div>
            
            <?php if (!$has_user_type): ?>
                <div class="update-section">
                    <h3>Update Required</h3>
                    <p>Your database needs to be updated to support user types. This will:</p>
                    <ul>
                        <li>Add a <code>user_type</code> column to the users table</li>
                        <li>Set all existing users to "help_seeker" type</li>
                        <li>Enable the new registration system with three user types</li>
                    </ul>
                    
                    <form method="post" action="update_user_types.php" class="auth-form">
                        <div class="form-actions">
                            <button type="submit" name="update_database" class="btn-primary">Update Database</button>
                        </div>
                    </form>
                </div>
            <?php else: ?>
                <div class="success-section">
                    <h3>✅ Database is Up to Date</h3>
                    <p>Your database already supports user types. You can now:</p>
                    <ul>
                        <li>Register new users with different account types</li>
                        <li>View user type badges on profiles</li>
                        <li>Use the enhanced registration system</li>
                    </ul>
                    
                    <div class="form-actions">
                        <a href="register.php" class="btn-primary">Test Registration</a>
                        <a href="test_user_types.php" class="btn-secondary">Test User Types</a>
                    </div>
                </div>
            <?php endif; ?>
            
            <div class="manual-update">
                <h3>Manual Update (Alternative)</h3>
                <p>If you prefer to update the database manually, run this SQL command:</p>
                <pre style="background: #f8f9fa; padding: 1rem; border-radius: 4px; overflow-x: auto;">
ALTER TABLE users 
ADD COLUMN user_type ENUM('help_seeker', 'business_organization', 'volunteer_mutual_aid') 
DEFAULT 'help_seeker' AFTER slug;

UPDATE users SET user_type = 'help_seeker' WHERE user_type IS NULL;
                </pre>
            </div>
        </div>
    </div>
</div>

<style>
.database-status, .update-section, .success-section, .manual-update {
    margin: 1.5rem 0;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 8px;
}

.database-status ul, .update-section ul, .success-section ul {
    margin: 1rem 0;
    padding-left: 2rem;
}

.database-status li, .update-section li, .success-section li {
    margin: 0.5rem 0;
}

.alert {
    padding: 1rem;
    border-radius: 4px;
    margin: 1rem 0;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

pre {
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
}

code {
    background-color: #e9ecef;
    padding: 0.2rem 0.4rem;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
}
</style>

<?php
// Include footer
include_once 'templates/components/footer.php';
?>
