/**
 * Spellbook Generator
 * 
 * This script creates a magical spellbook generator that displays random
 * pieces of magical advice in a stylized popup with wand cursor animations.
 */

// Wait for the DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Array of magical advice/spells
    const magicalAdvice = [
        "When in doubt, use <PERSON><PERSON><PERSON> to summon what you need.",
        "A well-cast Lumos can illuminate more than just dark corridors.",
        "Remember that Alohomora opens more than just locked doors.",
        "Expecto Patronum works best when you focus on your happiest memory.",
        "Never underestimate the power of a properly brewed Felix Felicis.",
        "Occlumency is essential when facing those who would invade your mind.",
        "A shield charm cast in time prevents the need for healing later.",
        "Transfiguration requires visualization, determination, and deliberation.",
        "The best potions are brewed with patience and precision.",
        "Wand movement is just as important as the incantation itself.",
        "Magical creatures respond best to respect and understanding.",
        "Divination may seem unclear, but the signs are always there for those who look.",
        "Even the simplest charm can be powerful when cast with intention.",
        "The best defense against dark magic is knowledge and preparation.",
        "Herbology teaches us that growth requires the right conditions and care.",
        "Flying is not just about the broom, but about trusting yourself.",
        "Ancient runes contain wisdom that transcends time and language.",
        "Astronomy reminds us that we are part of something much larger.",
        "Care of Magical Creatures begins with understanding their nature.",
        "History of Magic teaches us not to repeat the mistakes of the past."
    ];

    // Get the spellbook button element
    const spellbookButton = document.getElementById('spellbook-generator');
    
    // If the button doesn't exist, exit
    if (!spellbookButton) return;
    
    // Create the popup elements (but don't add to DOM yet)
    const popupOverlay = document.createElement('div');
    popupOverlay.className = 'spell-popup-overlay';
    
    const popupContent = document.createElement('div');
    popupContent.className = 'spell-popup-content';
    
    const popupHeader = document.createElement('div');
    popupHeader.className = 'spell-popup-header';
    popupHeader.innerHTML = '<h3>From the Ancient Spellbook</h3>';
    
    const popupBody = document.createElement('div');
    popupBody.className = 'spell-popup-body';
    
    const popupAdvice = document.createElement('p');
    popupAdvice.className = 'spell-advice';
    
    const popupClose = document.createElement('button');
    popupClose.className = 'spell-popup-close';
    popupClose.innerHTML = '&times;';
    
    const popupFooter = document.createElement('div');
    popupFooter.className = 'spell-popup-footer';
    popupFooter.innerHTML = '<p>Dogfarts School of Witchcraft and Wizardry</p>';
    
    // Assemble the popup
    popupBody.appendChild(popupAdvice);
    popupContent.appendChild(popupClose);
    popupContent.appendChild(popupHeader);
    popupContent.appendChild(popupBody);
    popupContent.appendChild(popupFooter);
    popupOverlay.appendChild(popupContent);
    
    // Add wand cursor element for animation
    const wandCursor = document.createElement('div');
    wandCursor.className = 'wand-cursor';
    document.body.appendChild(wandCursor);
    
    // Function to show the popup with a random piece of advice
    function showSpellPopup() {
        // Get a random piece of advice
        const randomIndex = Math.floor(Math.random() * magicalAdvice.length);
        const advice = magicalAdvice[randomIndex];
        
        // Set the advice text
        popupAdvice.textContent = advice;
        
        // Add the popup to the DOM
        document.body.appendChild(popupOverlay);
        
        // Add active class for animation
        setTimeout(() => {
            popupOverlay.classList.add('active');
            popupContent.classList.add('active');
        }, 10);
        
        // Add magical sparkle effect
        createSparkles(popupContent);
    }
    
    // Function to hide the popup
    function hideSpellPopup() {
        popupOverlay.classList.remove('active');
        popupContent.classList.remove('active');
        
        // Remove from DOM after animation completes
        setTimeout(() => {
            if (popupOverlay.parentNode) {
                document.body.removeChild(popupOverlay);
            }
        }, 500);
    }
    
    // Function to create sparkle effects
    function createSparkles(element) {
        for (let i = 0; i < 15; i++) {
            const sparkle = document.createElement('div');
            sparkle.className = 'sparkle';
            
            // Random position
            const left = Math.random() * 100;
            const top = Math.random() * 100;
            
            // Random size
            const size = Math.random() * 10 + 5;
            
            // Random animation duration
            const duration = Math.random() * 2 + 1;
            
            // Set styles
            sparkle.style.left = `${left}%`;
            sparkle.style.top = `${top}%`;
            sparkle.style.width = `${size}px`;
            sparkle.style.height = `${size}px`;
            sparkle.style.animationDuration = `${duration}s`;
            
            // Add to element
            element.appendChild(sparkle);
            
            // Remove after animation
            setTimeout(() => {
                if (sparkle.parentNode) {
                    sparkle.parentNode.removeChild(sparkle);
                }
            }, duration * 1000);
        }
    }
    
    // Function to update wand cursor position
    function updateWandPosition(e) {
        const x = e.clientX;
        const y = e.clientY;
        
        wandCursor.style.left = `${x}px`;
        wandCursor.style.top = `${y}px`;
    }
    
    // Function to handle button hover
    function handleButtonHover(e) {
        wandCursor.classList.add('active');
        
        // Create small sparkle on hover
        const sparkle = document.createElement('div');
        sparkle.className = 'wand-sparkle';
        wandCursor.appendChild(sparkle);
        
        // Remove sparkle after animation
        setTimeout(() => {
            if (sparkle.parentNode) {
                sparkle.parentNode.removeChild(sparkle);
            }
        }, 1000);
    }
    
    // Function to handle button hover end
    function handleButtonLeave() {
        wandCursor.classList.remove('active');
    }
    
    // Event listeners
    spellbookButton.addEventListener('click', showSpellPopup);
    popupClose.addEventListener('click', hideSpellPopup);
    popupOverlay.addEventListener('click', function(e) {
        if (e.target === popupOverlay) {
            hideSpellPopup();
        }
    });
    
    // Wand cursor event listeners
    document.addEventListener('mousemove', updateWandPosition);
    spellbookButton.addEventListener('mouseenter', handleButtonHover);
    spellbookButton.addEventListener('mouseleave', handleButtonLeave);
    
    // Escape key to close popup
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && document.body.contains(popupOverlay)) {
            hideSpellPopup();
        }
    });
});
