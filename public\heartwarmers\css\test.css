/* Simple test CSS file */
body {
    background-color: #f0f8ff !important;
    font-family: Arial, sans-serif !important;
    color: #333 !important;
}

.container {
    max-width: 1200px !important;
    margin: 0 auto !important;
    padding: 0 1rem !important;
}

.btn-primary {
    background-color: #007bff !important;
    color: white !important;
    padding: 0.75rem 1.5rem !important;
    border: none !important;
    border-radius: 4px !important;
    cursor: pointer !important;
    text-decoration: none !important;
    display: inline-block !important;
}

.btn-secondary {
    background-color: #6c757d !important;
    color: white !important;
    padding: 0.75rem 1.5rem !important;
    border: none !important;
    border-radius: 4px !important;
    cursor: pointer !important;
    text-decoration: none !important;
    display: inline-block !important;
}

.auth-page {
    padding: 2rem 0 !important;
    background-color: #f8f9fa !important;
    min-height: calc(100vh - 200px) !important;
}

.auth-card {
    max-width: 500px !important;
    margin: 0 auto !important;
    background-color: white !important;
    border-radius: 8px !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1) !important;
    padding: 2rem !important;
}

.auth-header {
    text-align: center !important;
    margin-bottom: 2rem !important;
}

.auth-header h1 {
    font-size: 1.5rem !important;
    margin-bottom: 0.5rem !important;
    color: #007bff !important;
}

.form-actions {
    display: flex !important;
    gap: 1rem !important;
    justify-content: center !important;
    flex-wrap: wrap !important;
    margin-top: 1rem !important;
}
