<?php
/**
 * Email Test Script
 * 
 * This script tests email functionality specifically
 * Use this to diagnose email delivery issues
 */

// Include configuration
require_once 'orders/config.php';

echo "<h1>Email Test Script</h1>";
echo "<style>body { font-family: Arial, sans-serif; } .success { color: green; } .error { color: red; } .info { color: blue; }</style>";

// Check if form was submitted
if ($_POST && isset($_POST['test_email'])) {
    $test_email = filter_var($_POST['test_email'], FILTER_VALIDATE_EMAIL);
    
    if (!$test_email) {
        echo "<p class='error'>Invalid email address provided</p>";
    } else {
        echo "<h2>Sending Test Email to: " . htmlspecialchars($test_email) . "</h2>";
        
        // Test 1: Simple text email
        echo "<h3>Test 1: Simple Text Email</h3>";
        $subject = "Test Email - Simple Text - " . date('Y-m-d H:i:s');
        $message = "This is a simple text email test from the Apple Chips order form.\n\nIf you receive this, basic email functionality is working.";
        $headers = "From: " . FROM_EMAIL . "\r\n";
        $headers .= "Reply-To: " . ADMIN_EMAIL . "\r\n";
        
        $result1 = mail($test_email, $subject, $message, $headers);
        echo "<p class='" . ($result1 ? 'success' : 'error') . "'>" . ($result1 ? '✓' : '✗') . " Simple email result: " . ($result1 ? 'Success' : 'Failed') . "</p>";
        
        // Test 2: HTML email (like customer confirmation)
        echo "<h3>Test 2: HTML Email (Customer Confirmation Style)</h3>";
        $subject2 = "Test Email - HTML Format - " . date('Y-m-d H:i:s');
        $html_message = "
        <html>
        <head>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .header { background: linear-gradient(135deg, #1C5D3B, #C33748); color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; }
                .test-info { background: #f9f9f9; padding: 15px; border-radius: 5px; margin: 10px 0; }
            </style>
        </head>
        <body>
            <div class='header'>
                <h2>🍎 Email Test - HTML Format 🍎</h2>
            </div>
            <div class='content'>
                <p>This is a test HTML email from the Apple Chips order form.</p>
                <div class='test-info'>
                    <p><strong>Test Time:</strong> " . date('Y-m-d H:i:s') . "</p>
                    <p><strong>From Email:</strong> " . FROM_EMAIL . "</p>
                    <p><strong>Admin Email:</strong> " . ADMIN_EMAIL . "</p>
                </div>
                <p>If you receive this email with proper formatting, HTML email functionality is working correctly.</p>
            </div>
        </body>
        </html>";
        
        $headers2 = "From: " . FROM_EMAIL . "\r\n";
        $headers2 .= "Reply-To: " . ADMIN_EMAIL . "\r\n";
        $headers2 .= "MIME-Version: 1.0\r\n";
        $headers2 .= "Content-Type: text/html; charset=UTF-8\r\n";
        $headers2 .= "X-Mailer: PHP/" . phpversion() . "\r\n";
        
        $result2 = mail($test_email, $subject2, $html_message, $headers2);
        echo "<p class='" . ($result2 ? 'success' : 'error') . "'>" . ($result2 ? '✓' : '✗') . " HTML email result: " . ($result2 ? 'Success' : 'Failed') . "</p>";
        
        // Test 3: Admin notification style
        echo "<h3>Test 3: Admin Notification Style</h3>";
        $subject3 = "Test Email - Admin Notification - " . date('Y-m-d H:i:s');
        $admin_message = "
        <html>
        <head>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .header { background: #8B4513; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; }
                .test-details { background: #f9f9f9; padding: 15px; border-radius: 5px; margin: 10px 0; }
            </style>
        </head>
        <body>
            <div class='header'>
                <h2>🍎 Test Admin Notification 🍎</h2>
            </div>
            <div class='content'>
                <div class='test-details'>
                    <p><strong>Test Time:</strong> " . date('Y-m-d H:i:s') . "</p>
                    <p><strong>Test Email:</strong> " . htmlspecialchars($test_email) . "</p>
                    <p><strong>Server:</strong> " . $_SERVER['HTTP_HOST'] . "</p>
                    <p><strong>PHP Version:</strong> " . phpversion() . "</p>
                </div>
            </div>
        </body>
        </html>";
        
        $headers3 = "From: " . FROM_EMAIL . "\r\n";
        $headers3 .= "Reply-To: " . $test_email . "\r\n";
        $headers3 .= "MIME-Version: 1.0\r\n";
        $headers3 .= "Content-Type: text/html; charset=UTF-8\r\n";
        $headers3 .= "X-Mailer: PHP/" . phpversion() . "\r\n";
        
        $result3 = mail(ADMIN_EMAIL, $subject3, $admin_message, $headers3);
        echo "<p class='" . ($result3 ? 'success' : 'error') . "'>" . ($result3 ? '✓' : '✗') . " Admin notification result: " . ($result3 ? 'Success' : 'Failed') . "</p>";
        
        echo "<h3>Summary</h3>";
        $total_success = ($result1 ? 1 : 0) + ($result2 ? 1 : 0) + ($result3 ? 1 : 0);
        echo "<p class='info'>Tests completed: 3</p>";
        echo "<p class='info'>Successful: " . $total_success . "</p>";
        echo "<p class='info'>Failed: " . (3 - $total_success) . "</p>";
        
        if ($total_success === 0) {
            echo "<p class='error'>All email tests failed. Check your server's mail configuration.</p>";
        } elseif ($total_success < 3) {
            echo "<p class='info'>Some email tests failed. Check server logs for specific errors.</p>";
        } else {
            echo "<p class='success'>All email tests passed! Email functionality appears to be working.</p>";
        }
    }
}
?>

<h2>Email Configuration Info</h2>
<p class="info"><strong>From Email:</strong> <?php echo FROM_EMAIL; ?></p>
<p class="info"><strong>Admin Email:</strong> <?php echo ADMIN_EMAIL; ?></p>
<p class="info"><strong>Server:</strong> <?php echo $_SERVER['HTTP_HOST']; ?></p>
<p class="info"><strong>PHP Mail Function:</strong> <?php echo function_exists('mail') ? 'Available' : 'Not Available'; ?></p>

<?php if (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false): ?>
<div style="background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #ffc107;">
    <h3>Localhost Notice</h3>
    <p>You're running on localhost. Email functionality typically requires additional configuration:</p>
    <ul>
        <li>Configure SMTP settings in php.ini</li>
        <li>Use a local mail server like MailHog or Papercut</li>
        <li>Use a service like Mailtrap for testing</li>
    </ul>
</div>
<?php endif; ?>

<h2>Test Email Sending</h2>
<form method="post" style="background: #f9f9f9; padding: 20px; border-radius: 5px;">
    <p>
        <label for="test_email"><strong>Enter email address to test:</strong></label><br>
        <input type="email" id="test_email" name="test_email" required style="width: 300px; padding: 8px; margin-top: 5px;">
    </p>
    <p>
        <button type="submit" style="background: #1C5D3B; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">Send Test Emails</button>
    </p>
    <p style="font-size: 0.9em; color: #666;">
        This will send 3 test emails: simple text, HTML customer confirmation style, and admin notification style.
    </p>
</form>
