<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modernizing Healthcare Service Delivery in North Carolina</title>
    <head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modernizing Healthcare Service Delivery in North Carolina</title>
    <meta name="description" content="Advocate for accessible, flexible, and inclusive virtual care solutions in North Carolina. Learn about objectives, benefits, and join the movement for change in mental healthcare." />
    <meta name="keywords" content="virtual care, telehealth, mental healthcare, north carolina, HHS, pee support specialist, disability, rural, domestic violence, immunocompromised, petition, sign the petition, share your story" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://www.yourdomain.com/" />
    <meta property="og:title" content="Modernizing Healthcare Service Delivery in North Carolina" />
    <meta property="og:description" content="Join the effort to bring accessible, flexible, and inclusive virtual care solutions to North Carolina. Help raise awareness and effect positive change in mental healthcare." />
    <meta property="og:image" content="https://www.yourdomain.com/images/preview.png" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@yourhandle" />
    <meta name="twitter:title" content="Modernizing Healthcare Service Delivery in North Carolina" />
    <meta name="twitter:description" content="Work together to promote accessible, flexible, and inclusive virtual care solutions in North Carolina. Contribute to shaping mental healthcare for the future." />
    <meta name="twitter:image" content="https://www.yourdomain.com/images/preview.png" />
    <link rel="canonical" href="https://www.yourdomain.com/" />
    <style>
        :root {
  --color-primary: hsl(var(--hue-primary), var(--saturation-primary), var(--lightness-primary));
  --color-secondary: hsl(var(--hue-secondary), var(--saturation-secondary), var(--lightness-secondary));
  --color-background: hsl(var(--hue-background), var(--saturation-background), var(--lightness-background));
}

*,
::before,
::after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Roboto', sans-serif;
  line-height: 1.6;
  font-size: 1rem;
  color: var(--color-text);
  background-color: var(--color-background);
  scroll-behavior: smooth;
}

h1, h2, h3, h4, h5, h6 {
  line-height: 1.2;
}

a {
  text-decoration: none;
  color: inherit;
}

button, input, select, optgroup, textarea {
  appearance: none;
  outline: none;
  user-select: none;
  resize: vertical;
  -webkit-appearance: none;
}

input, textarea, select {
  display: block;
  width: 100%;
  border: solid 1px var(--color-border);
  background-color: var(--color-white);
  padding: .5em;
}

input:focus, textarea:focus, select:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 1px 1px var(--color-primary);
}

button {
  cursor: pointer;
  border: none;
  padding: .5em calc(1em + .5ch);
  background-color: var(--color-primary);
  color: var(--color-background);
  transition: transform .2s ease-out;
}

button:hover {
  background-color: var(--color-secondary);
  transform: translateY(-2px);
}

button:active {
  transform: translateY(2px);
}

button:disabled {
  opacity: .5;
  cursor: default;
}

/* Layout */
.grid-two-cols {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 2rem;
  max-width: 1200px;
  margin: auto;
  padding: 2rem 1rem;
}

.grid-two-cols > div {
  flex: 1 1 minmax(0, 50%);
}

.grid-four-cols {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 2rem;
  max-width: 1200px;
  margin: auto;
  padding: 2rem 1rem;
}

.grid-four-cols > article {
  flex: 1 1 minmax(0, 25%);
  text-align: center;
}

.card {
  background-color: var(--color-white);
  border-radius: 5px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.card h3 {
  margin-bottom: .5rem;
}

.card p {
  margin-bottom: 1rem;
}

.social-links li {
  margin-right: 1rem;
}

.social-links i {
  font-size: 1.2rem;
  transition: transform .2s ease-out;
}

.social-links i:hover {
  transform: scale(1.2);
}

.modal-trigger {
  display: inline-block;
}

/* Modal window */
.modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0);
  visibility: hidden;
  z-index: -1;
  background-color: var(--color-white);
  border-radius: 5px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
  padding: 2rem;
  max-width: 80%;
  width: 500px;
  animation: slide-down .5s cubic-bezier(0.77, 0, 0.175, 1) forwards;
}

@keyframes slide-down {
  0% {transform: translate(-50%, -50%) scale(0); visibility: visible;}
  80% {transform: translate(-50%, -50%) rotateX(-10deg);}
  90% {transform: translate(-50%, -50%) rotateX(0);}
  100% {transform: translate(-50%, -50%) scale(1); visibility: visible;}
}

.modal__close {
  position: absolute;
  top: 1rem;
  right: 1rem;
  cursor: pointer;
  border: none;
  background: transparent;
  font-size: 2rem;
}

.modal:target {z-index: 1000;}

.modal:target ~ .overlay {display: block;}

.overlay {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: none;
}

/* Hero section */
.hero {
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: scroll;
  background-size: cover;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  color: white;
  position: relative;
}

.hero::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('hero-bg.jpg');
  filter: brightness(70%);
}

.hero img {
  width: 150px;
  height: 150px;
  object-fit: cover;
  border-radius: 50%;
  margin-bottom: 2rem;
}

.hero h1 {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.hero h2 {
  font-size: 1.8rem;
  font-weight: normal;
}

.hero p {
  font-size: 1.2rem;
  margin-top: 2rem;
  max-width: 600px;
}

/* Utility classes */
.container {
  max-width: 1200px;
  margin: auto;
  padding: 2rem 1rem;
}

.grid-two-cols {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 2rem;
  max-width: 1200px;
  margin: auto;
  padding: 2rem 1rem;
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1rem;
  gap: 0.5rem;
  border-radius: 0.5rem;
  font-size: 1rem;
  line-height: 1;
  text-align: center;
  text-decoration: none;
  touch-action: manipulation;
  cursor: pointer;
  background-color: var(--color-primary);
  color: var(--color-white);
  transition: filter 0.15s ease-out;
}

.btn:not(:disabled):hover {
  filter: contrast(115%);
}

.btn:focus {
  box-shadow: 0 0 0 1px black;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn:not(:disabled).btn--loading {
  pointer-events: none;
}

.btn--icon-left svg {
  width: 1.25rem;
  height: 1.25rem;
}

.btn--icon-right svg {
  width: 1.25rem;
  height: 1.25rem;
  fill: currentColor;
  opacity: 0.5;
}

.btn--icon-right:hover svg {
  opacity: 0.75;
}

@media screen and (min-width: 768px) {
  body {font-size: 1.1rem;}
}

@media screen and (min-width: 1024px) {
  .hero h1 {font-size: 4rem;}
}
    </style>
    <!-- Add CSS stylesheets here -->
</head>
<body>
    <header class="hero">
        <h1>Modernizing Healthcare Service Delivery in North Carolina</h1>
        <h2>Embracing virtual care and digital literacy for improved access and inclusivity</h2>
    </header>

    <section id="intro">
        <div class="container">
            <img src="path/to/april_image.jpg" alt="April Cyr image" width="150" height="150">
            <blockquote>
                <p>"As someone who has experienced mental health challenges myself, I recognize the importance of timely and accessible support. Virtual care solutions, like chat-based services, have the potential to bridge gaps in traditional mental health and crisis support systems, particularly for those living in rural areas or facing unique circumstances." - April Cyr</p>
            </blockquote>
        </div>
    </section>

    <section id="objective">
        <h2>Our Objectives</h2>
        <div class="container grid-two-cols">
            <div>
                <p><strong>1.</strong> Establish virtual seminars and self-paced online courses for HHS training:</p>
                <ul>
                    <li>Promote approved online courses, like CASAT Learning Group's Peer Support Specialist Training 101;</li>
                </ul>
            </div>
            <div>
                <p><strong>2.</strong> Explore safe and standardized virtual care solutions for paid specialists and disabled clientele.</p>
                <p><strong>3.</strong> Implement computer skills training via self-paced courses for vulnerable adults.</p>
            </div>
            <div>
                <p><strong>4.</strong> Collaborate with other states regarding supported decision-making, safeguarding, and virtual employment programs.</p>
            </div>
            <div>
                <p><strong>5.</strong> Secure funding for expanding virtual care initiatives through relevant grants.</p>
                <p><strong>6.</strong> Prioritize transparency in staff rosters and departments within VAYA Health.</p>
            </div>
        </div>
    </section>

    <section id="benefits">
        <h2>Benefits of Virtual Care</h2>
        <div class="container grid-four-cols">
            <article>
                <i class="fas fa-map-marker-alt"></i>
                <h3>Increased Access</h3>
                <p>Provide mental health services to underserved populations in rural areas.</p>
            </article>
            <article>
                <i class="fas fa-calendar-alt"></i>
                <h3>Flexibility</h3>
                <p>Offer greater scheduling freedom for busy single parents.</p>
            </article>
            <article>
                <i class="fas fa-lock"></i>
                <h3>Privacy</h3>
                <p>Ensure safer conversations for survivors of domestic violence.</p>
            </article>
            <article>
                <i class="fas fa-wheelchair"></i>
                <h3>Reduced Barriers</h3>
                <p>Minimize obstacles for disabled and immunocompromised individuals.</p>
            </article>
        </div>
    </section>

    <section id="cta">
        <h2>Take Action Now</h2>
        <div class="container grid-three-cols gap-lg">
            <div class="card action-item">
                <h3>Sign the Petition</h3>
                <p>Help create a more inclusive and supportive environment for North Carolinians by signing the petition demanding expanded virtual service access.</p>
                <a href="#petition-modal" class="button button--primary modal-trigger">Sign the Petition</a>
            </div>
            <div class="card action-item">
                <h3>Share Your Story</h3>
                <p>Contribute to raising awareness by sharing your personal experiences with virtual care. Fill out the form linked below.</p>
                <a href="#story-form-modal" class="button button--secondary modal-trigger">Share Your Story</a>
            </div>
            <div class="card action-item">
                <h3>Stay Updated</h3>
                <p>Connect with the community and follow the latest updates using the hashtags #TelehealthForAll, #AccessIsEssential, and #VulnerableCommunities.</p>
                <ul class="social-links list-inline">
                    <li><a href="#"><i class="fab fa-facebook-f"></i></a></li>
                    <li><a href="#"><i class="fab fa-twitter"></i></a></li>
                    <li><a href="#"><i class="fab fa-instagram"></i></a></li>
                </ul>
            </div>
        </div>
    </section>

    <!-- Modal windows for petitions and forms -->
    <div id="petition-modal" class="modal">...</div>
    <div id="story-form-modal" class="modal">...</div>
    
    <!-- Scripts -->
    <!-- Add JavaScript files here -->
</body>
</html>