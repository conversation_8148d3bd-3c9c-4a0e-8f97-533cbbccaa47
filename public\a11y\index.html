<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Accessible Digital Note-Taking | Your Name</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/accessibility.css" disabled>
</head>
<body>
    <a class="skip-link" href="#main-content">Skip to main content</a>
    
    <header class="site-header">
        <div class="container">
            <h1 class="site-title">Your Name</h1>
            <p class="site-tagline">Digital Accessibility Specialist</p>
            
            <nav aria-label="Main navigation">
                <button id="menu-toggle" aria-expanded="false" aria-controls="main-menu">Menu</button>
                <ul id="main-menu">
                    <li><a href="index.html" aria-current="page">Home</a></li>
                    <li><a href="resources.html">Accessibility Resources</a></li>
                    <li><a href="obsidian.html">Obsidian Help</a></li>
                    <li><a href="services.html">Services</a></li>
                    <li><a href="about.html">About</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main id="main-content">
        <section class="hero">
            <div class="container">
                <h2>Empowering Accessible Digital Note-Taking</h2>
                <p>Helping passionate note-takers and knowledge workers create accessible digital workflows, with a special focus on Obsidian.</p>
                <div class="cta-buttons">
                    <a href="resources.html" class="button">Learn Accessibility Basics</a>
                    <a href="obsidian.html" class="button secondary">Get Obsidian Help</a>
                </div>
            </div>
        </section>

        <section class="featured-resources">
            <div class="container">
                <h2>Featured Accessibility Resources</h2>
                <div class="resource-grid">
                    <article class="resource-card">
                        <h3><a href="resources.html#screen-readers">Screen Reader Compatibility</a></h3>
                        <p>Learn how to make your content work with popular screen readers like NVDA and JAWS.</p>
                    </article>
                    <article class="resource-card">
                        <h3><a href="resources.html#keyboard-nav">Keyboard Navigation</a></h3>
                        <p>Essential techniques for keyboard-only users to navigate digital content.</p>
                    </article>
                    <article class="resource-card">
                        <h3><a href="obsidian.html">Obsidian for Accessibility</a></h3>
                        <p>Specialized guidance for using Obsidian with assistive technologies.</p>
                    </article>
                </div>
            </div>
        </section>

        <section class="services-preview">
            <div class="container">
                <h2>Personalized Support</h2>
                <p>Get one-on-one help with your specific accessibility challenges.</p>
                <a href="services.html" class="button">View Services</a>
            </div>
        </section>
    </main>

    <footer class="site-footer">
        <div class="container">
            <div class="footer-contact">
                <h3>Contact Me</h3>
                <p>Email: <a href="mailto:<EMAIL>"><EMAIL></a></p>
            </div>
            <div class="footer-accessibility">
                <button id="a11y-toggle">Toggle Accessibility Mode</button>
                <button id="font-increase">Increase Font Size</button>
                <button id="font-decrease">Decrease Font Size</button>
            </div>
        </div>
    </footer>

    <script src="js/main.js"></script>
    <script src="js/a11y.js"></script>
</body>
</html>