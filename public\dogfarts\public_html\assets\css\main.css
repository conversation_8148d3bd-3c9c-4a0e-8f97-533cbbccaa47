/* Main stylesheet for Dogfarts School website */

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

a {
    color: #0066cc;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

h1, h2, h3, h4, h5, h6 {
    margin-bottom: 0.5em;
    font-weight: bold;
    color: #333;
}

p {
    margin-bottom: 1em;
}

/* Layout */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
    display: grid;
    grid-template-columns: 1fr 300px;
    grid-gap: 30px;
}

header {
    background-color: #4a2c82;
    color: white;
    padding: 20px 0;
}

.logo {
    display: flex;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

.logo img {
    height: 60px;
    margin-right: 15px;
}

.logo h1 {
    font-size: 1.8em;
    color: white;
    margin: 0;
}

/* Navigation */
.main-nav {
    background-color: #333;
    padding: 10px 0;
}

.main-nav ul {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
    list-style: none;
    display: flex;
}

.main-nav li {
    margin-right: 20px;
}

.main-nav a {
    color: white;
    text-decoration: none;
    padding: 5px 10px;
    border-radius: 3px;
}

.main-nav a:hover, .main-nav a.active {
    background-color: #4a2c82;
}

/* Main content */
main {
    padding: 30px 0;
}

main h1 {
    font-size: 2em;
    margin-bottom: 20px;
    color: #4a2c82;
}

/* Sidebar */
.sidebar {
    padding: 30px 0;
}

.widget {
    background-color: white;
    border-radius: 5px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.widget h3 {
    border-bottom: 2px solid #4a2c82;
    padding-bottom: 10px;
    margin-bottom: 15px;
}

.widget ul {
    list-style: none;
}

.widget li {
    margin-bottom: 10px;
}

/* Footer */
footer {
    background-color: #333;
    color: white;
    padding: 30px 0;
    text-align: center;
}

.footer-nav ul {
    list-style: none;
    display: flex;
    justify-content: center;
    margin-top: 15px;
}

.footer-nav li {
    margin: 0 10px;
}

.footer-nav a {
    color: white;
}

/* Admin styles */
.admin-login {
    background-color: #f5f5f5;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
}

.login-container {
    background-color: white;
    padding: 30px;
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    width: 100%;
    max-width: 400px;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
}

.form-group input {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 3px;
}

button {
    background-color: #4a2c82;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 3px;
    cursor: pointer;
}

button:hover {
    background-color: #3a2065;
}

.error {
    background-color: #ffebee;
    color: #c62828;
    padding: 10px;
    border-radius: 3px;
    margin-bottom: 15px;
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        grid-template-columns: 1fr;
    }
    
    .main-nav ul {
        flex-direction: column;
    }
    
    .main-nav li {
        margin-right: 0;
        margin-bottom: 10px;
    }
    
    .footer-nav ul {
        flex-direction: column;
    }
    
    .footer-nav li {
        margin: 5px 0;
    }
}
