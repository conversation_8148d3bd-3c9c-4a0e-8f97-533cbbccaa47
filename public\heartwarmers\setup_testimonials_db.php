<?php
/**
 * Setup script for testimonials database tables
 * Run this script to create the testimonials tables if they don't exist
 */

// Include necessary files
require_once 'php/includes/db.php';
require_once 'php/includes/functions.php';

// Set page variables
$pageTitle = 'Setup Testimonials Database - Heartwarmers';
$pageDescription = 'Setup the testimonials database tables';
$currentPage = 'admin';

$message = '';
$messageType = '';

// Process setup if requested
if (isset($_POST['setup_testimonials'])) {
    $conn = get_db_connection();
    
    if (!$conn) {
        $message = 'Database connection failed.';
        $messageType = 'error';
    } else {
        try {
            // Read the SQL file
            $sql_file = 'sql/testimonials_schema.sql';
            if (!file_exists($sql_file)) {
                $message = 'Testimonials schema file not found.';
                $messageType = 'error';
            } else {
                $sql_content = file_get_contents($sql_file);
                
                // Split the SQL into individual statements
                $statements = explode(';', $sql_content);
                $success_count = 0;
                $error_count = 0;
                $errors = [];
                
                foreach ($statements as $statement) {
                    $statement = trim($statement);
                    if (empty($statement) || strpos($statement, '--') === 0) {
                        continue; // Skip empty statements and comments
                    }
                    
                    // Skip DELIMITER statements (MySQL specific)
                    if (strpos($statement, 'DELIMITER') !== false) {
                        continue;
                    }
                    
                    try {
                        if ($conn->query($statement)) {
                            $success_count++;
                        } else {
                            $error_count++;
                            $errors[] = $conn->error;
                        }
                    } catch (Exception $e) {
                        $error_count++;
                        $errors[] = $e->getMessage();
                    }
                }
                
                if ($error_count == 0) {
                    $message = "Successfully executed $success_count SQL statements. Testimonials database is ready!";
                    $messageType = 'success';
                } else {
                    $message = "Executed $success_count statements successfully, but $error_count failed. Errors: " . implode('; ', array_slice($errors, 0, 3));
                    $messageType = 'warning';
                }
            }
            
        } catch (Exception $e) {
            $message = 'Database error: ' . $e->getMessage();
            $messageType = 'error';
        }
    }
}

// Check current database status
$conn = get_db_connection();
$tables_exist = [];
$table_names = ['user_testimonials', 'testimonial_categories', 'testimonial_category_assignments', 'testimonial_settings', 'testimonial_reports'];

if ($conn) {
    foreach ($table_names as $table) {
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        $tables_exist[$table] = ($result && $result->num_rows > 0);
    }
}

// Include header
include_once 'templates/components/header.php';
?>

<div class="auth-page">
    <div class="container">
        <div class="auth-card">
            <div class="auth-header">
                <h1>Setup Testimonials Database</h1>
                <p>Create the necessary database tables for the testimonials system</p>
            </div>
            
            <?php if (!empty($message)): ?>
                <div class="alert alert-<?php echo $messageType; ?>">
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>
            
            <div class="database-status">
                <h3>Current Database Status:</h3>
                <ul>
                    <li><strong>Database Connection:</strong> <?php echo $conn ? '✅ Connected' : '❌ Failed'; ?></li>
                    <?php foreach ($tables_exist as $table => $exists): ?>
                        <li><strong><?php echo ucfirst(str_replace('_', ' ', $table)); ?>:</strong> 
                            <?php echo $exists ? '✅ Exists' : '❌ Missing'; ?>
                        </li>
                    <?php endforeach; ?>
                </ul>
            </div>
            
            <?php 
            $all_tables_exist = array_reduce($tables_exist, function($carry, $exists) {
                return $carry && $exists;
            }, true);
            ?>
            
            <?php if (!$all_tables_exist): ?>
                <div class="setup-section">
                    <h3>Setup Required</h3>
                    <p>Some testimonials database tables are missing. This will create:</p>
                    <ul>
                        <li><code>user_testimonials</code> - Main testimonials table</li>
                        <li><code>testimonial_categories</code> - Categories for organizing testimonials</li>
                        <li><code>testimonial_category_assignments</code> - Links testimonials to categories</li>
                        <li><code>testimonial_settings</code> - User preferences for testimonials</li>
                        <li><code>testimonial_reports</code> - Reporting system for inappropriate content</li>
                    </ul>
                    
                    <form method="post" action="setup_testimonials_db.php" class="auth-form">
                        <div class="form-actions">
                            <button type="submit" name="setup_testimonials" class="btn-primary">Setup Testimonials Database</button>
                        </div>
                    </form>
                </div>
            <?php else: ?>
                <div class="success-section">
                    <h3>✅ Testimonials Database is Ready</h3>
                    <p>All testimonials tables exist. You can now:</p>
                    <ul>
                        <li>Submit testimonials for users</li>
                        <li>View testimonials on user profiles</li>
                        <li>Moderate testimonials through the admin panel</li>
                        <li>Configure testimonial settings</li>
                    </ul>
                    
                    <div class="form-actions">
                        <a href="submit-testimonial.php" class="btn-primary">Test Submit Testimonial</a>
                        <a href="admin/testimonials.php" class="btn-secondary">Admin Panel</a>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
.database-status, .setup-section, .success-section {
    margin: 1.5rem 0;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 8px;
}

.database-status ul, .setup-section ul, .success-section ul {
    margin: 1rem 0;
    padding-left: 2rem;
}

.database-status li, .setup-section li, .success-section li {
    margin: 0.5rem 0;
}

.alert {
    padding: 1rem;
    border-radius: 4px;
    margin: 1rem 0;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

code {
    background-color: #e9ecef;
    padding: 0.2rem 0.4rem;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
}
</style>

<?php
// Include footer
include_once 'templates/components/footer.php';
?>
