<?php
/**
 * Debug Test Script for Order Form Issues
 * 
 * This script helps identify specific problems with:
 * - File permissions
 * - Email configuration
 * - Google Sheets connectivity
 * - Directory structure
 */

// Include configuration
require_once 'orders/config.php';

echo "<h1>Order Form Debug Test</h1>";
echo "<style>body { font-family: Arial, sans-serif; } .success { color: green; } .error { color: red; } .info { color: blue; }</style>";

// Test 1: Configuration Loading
echo "<h2>1. Configuration Test</h2>";
if (defined('ADMIN_EMAIL')) {
    echo "<p class='success'>✓ Config loaded successfully</p>";
    echo "<p class='info'>Admin Email: " . ADMIN_EMAIL . "</p>";
    echo "<p class='info'>From Email: " . FROM_EMAIL . "</p>";
    echo "<p class='info'>Google Webapp URL: " . GOOGLE_WEBAPP_URL . "</p>";
} else {
    echo "<p class='error'>✗ Config failed to load</p>";
}

// Test 2: Directory and File Permissions
echo "<h2>2. File System Test</h2>";
$orders_dir = ORDERS_DIRECTORY;
echo "<p class='info'>Orders Directory: " . $orders_dir . "</p>";

if (is_dir($orders_dir)) {
    echo "<p class='success'>✓ Orders directory exists</p>";
    
    if (is_writable($orders_dir)) {
        echo "<p class='success'>✓ Orders directory is writable</p>";
    } else {
        echo "<p class='error'>✗ Orders directory is not writable</p>";
        echo "<p class='info'>Directory permissions: " . substr(sprintf('%o', fileperms($orders_dir)), -4) . "</p>";
    }
} else {
    echo "<p class='error'>✗ Orders directory does not exist</p>";
    echo "<p class='info'>Attempting to create directory...</p>";
    if (mkdir($orders_dir, 0755, true)) {
        echo "<p class='success'>✓ Directory created successfully</p>";
    } else {
        echo "<p class='error'>✗ Failed to create directory</p>";
    }
}

// Test CSV file
$csv_file = $orders_dir . ORDERS_CSV_FILE;
echo "<p class='info'>CSV File: " . $csv_file . "</p>";

if (file_exists($csv_file)) {
    echo "<p class='success'>✓ CSV file exists</p>";
    if (is_writable($csv_file)) {
        echo "<p class='success'>✓ CSV file is writable</p>";
    } else {
        echo "<p class='error'>✗ CSV file is not writable</p>";
    }
} else {
    echo "<p class='info'>CSV file does not exist (will be created on first order)</p>";
}

// Test 3: Email Configuration
echo "<h2>3. Email Test</h2>";
$test_email = "<EMAIL>";
$test_subject = "Test Email - " . date('Y-m-d H:i:s');
$test_message = "This is a test email from the order form debug script.";
$test_headers = "From: " . FROM_EMAIL . "\r\n";
$test_headers .= "MIME-Version: 1.0\r\n";
$test_headers .= "Content-Type: text/plain; charset=UTF-8\r\n";

echo "<p class='info'>Testing email function...</p>";
echo "<p class='info'>From: " . FROM_EMAIL . "</p>";
echo "<p class='info'>To: " . $test_email . "</p>";

// Don't actually send the test email, just check if mail function exists
if (function_exists('mail')) {
    echo "<p class='success'>✓ Mail function is available</p>";
    
    // Check if we're on localhost
    if (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false) {
        echo "<p class='info'>Running on localhost - mail() will likely fail without SMTP configuration</p>";
        echo "<p class='info'>For localhost testing, configure SMTP in php.ini or use a service like MailHog</p>";
    } else {
        echo "<p class='info'>Running on web server - mail() should work if server is configured properly</p>";
    }
} else {
    echo "<p class='error'>✗ Mail function is not available</p>";
}

// Test 4: Google Sheets Connectivity
echo "<h2>4. Google Sheets Test</h2>";
echo "<p class='info'>Testing Google Sheets connectivity...</p>";

$test_data = [
    'order_id' => 'TEST_' . uniqid(),
    'timestamp' => date('Y-m-d H:i:s'),
    'name' => 'Test User',
    'email' => '<EMAIL>',
    'phone' => '************',
    'address' => '123 Test St',
    'city' => 'Test City',
    'state' => 'TS',
    'zip' => '12345',
    'bags' => 1,
    'payment' => 'PayPal',
    'message' => 'Test order',
    'instructions' => 'Test instructions'
];

$postData = [
    'order_data' => json_encode($test_data),
    'token' => GOOGLE_APP_TOKEN
];

$options = [
    'http' => [
        'header'  => "Content-type: application/x-www-form-urlencoded\r\n",
        'method'  => 'POST',
        'content' => http_build_query($postData),
        'timeout' => 30
    ],
];

$context = stream_context_create($options);
$result = @file_get_contents(GOOGLE_WEBAPP_URL, false, $context);

if ($result !== false) {
    echo "<p class='success'>✓ Google Sheets request successful</p>";
    echo "<p class='info'>Response: " . htmlspecialchars($result) . "</p>";
    
    $response = json_decode($result, true);
    if ($response && isset($response['success'])) {
        if ($response['success']) {
            echo "<p class='success'>✓ Google Sheets reported success</p>";
        } else {
            echo "<p class='error'>✗ Google Sheets reported failure</p>";
        }
    } else {
        if (stripos($result, 'success') !== false) {
            echo "<p class='success'>✓ Google Sheets response contains 'success'</p>";
        } else {
            echo "<p class='error'>✗ Google Sheets response does not indicate success</p>";
        }
    }
} else {
    echo "<p class='error'>✗ Failed to connect to Google Sheets</p>";
    $error = error_get_last();
    if ($error) {
        echo "<p class='error'>Error: " . htmlspecialchars($error['message']) . "</p>";
    }
}

// Test 5: PHP Configuration
echo "<h2>5. PHP Configuration</h2>";
echo "<p class='info'>PHP Version: " . phpversion() . "</p>";
echo "<p class='info'>Error Reporting: " . (error_reporting() ? 'Enabled' : 'Disabled') . "</p>";
echo "<p class='info'>Display Errors: " . (ini_get('display_errors') ? 'On' : 'Off') . "</p>";
echo "<p class='info'>Log Errors: " . (ini_get('log_errors') ? 'On' : 'Off') . "</p>";
echo "<p class='info'>Error Log: " . (ini_get('error_log') ?: 'Default') . "</p>";

// Test 6: Session Test
echo "<h2>6. Session Test</h2>";
if (session_status() === PHP_SESSION_ACTIVE) {
    echo "<p class='success'>✓ Session is active</p>";
} else {
    echo "<p class='error'>✗ Session is not active</p>";
}

echo "<h2>Debug Test Complete</h2>";
echo "<p class='info'>Check your server's error logs for additional debugging information.</p>";
echo "<p class='info'>Common error log locations:</p>";
echo "<ul>";
echo "<li>cPanel: /home/<USER>/public_html/error_logs/</li>";
echo "<li>Apache: /var/log/apache2/error.log</li>";
echo "<li>XAMPP: /xampp/apache/logs/error.log</li>";
echo "</ul>";
?>
