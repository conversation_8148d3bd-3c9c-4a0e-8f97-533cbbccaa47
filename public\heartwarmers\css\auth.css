/**
 * Authentication pages styles for Heartwarmers website
 */

.auth-page {
    padding: var(--spacing-xxl) 0;
    background-color: var(--bg-light);
    min-height: calc(100vh - 200px);
    display: flex;
    align-items: center;
}

.auth-card {
    max-width: 500px;
    margin: 0 auto;
    background-color: white;
    border-radius: var(--border-radius-lg);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    padding: var(--spacing-xl);
}

.auth-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.auth-header h1 {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-sm);
    color: var(--primary-color);
}

.auth-header p {
    color: var(--text-light);
}

.auth-form {
    margin-bottom: var(--spacing-lg);
}

.form-group {
    margin-bottom: var(--spacing-md);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: bold;
}

.form-group input {
    width: 100%;
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-md);
}

.form-help {
    font-size: var(--font-size-sm);
    color: var(--text-light);
    margin-top: var(--spacing-xs);
}

.form-actions {
    margin-top: var(--spacing-lg);
}

.form-actions button {
    width: 100%;
    padding: var(--spacing-md);
    font-size: var(--font-size-md);
}

.auth-links {
    margin-top: var(--spacing-lg);
    text-align: center;
    font-size: var(--font-size-sm);
}

.auth-links p {
    margin-bottom: var(--spacing-sm);
}

.auth-links a {
    color: var(--primary-color);
    text-decoration: none;
}

.auth-links a:hover {
    text-decoration: underline;
}

.alert {
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-lg);
}

.alert-error {
    background-color: var(--bg-error);
    color: var(--text-error);
    border: 1px solid var(--border-error);
}

.alert-success {
    background-color: var(--bg-success);
    color: var(--text-success);
    border: 1px solid var(--border-success);
}

/* User Type Selection */
#user-type-descriptions {
    margin-top: var(--spacing-xs);
}

.user-type-desc {
    margin: 0;
    padding: var(--spacing-sm);
    background-color: var(--bg-light);
    border-radius: var(--border-radius);
    border-left: 3px solid var(--primary-color);
    font-style: italic;
    transition: all 0.3s ease;
}

.form-group select {
    width: 100%;
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: var(--font-size-base);
    background-color: white;
    cursor: pointer;
}

.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.2);
}

/* Responsive */
@media (max-width: 768px) {
    .auth-card {
        padding: var(--spacing-lg);
    }
}
