/* 
 * School-Themed Website - Mobile-First CSS Layout
 * 
 * This stylesheet implements a responsive, mobile-first design for a school-themed website.
 * It uses CSS Grid for the main layout and Flexbox for card-based blog entries.
 */

/* ---------- Base Styles & CSS Reset ---------- */
*, *::before, *::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* ---------- Variables ---------- */
:root {
  /* Color Palette */
  --primary: #8e3200;         /* Deep maroon/burgundy */
  --primary-light: #a84a00;   /* Lighter maroon */
  --secondary: #d4a76a;       /* Parchment tan */
  --secondary-light: #e9d2b3; /* Light parchment */
  --accent: #1d3557;          /* Deep blue */
  --accent-light: #457b9d;    /* Lighter blue */
  --text-dark: #2b2b2b;       /* Almost black */
  --text-light: #f8f8f8;      /* Off-white */
  --background: #f5f1e9;      /* Very light parchment */
  --success: #2e7d32;         /* Green */
  --warning: #f9a825;         /* Amber */
  --error: #c62828;           /* Red */
  
  /* Typography */
  --font-heading: 'Playfair Display', Georgia, serif;
  --font-body: 'Raleway', 'Segoe UI', Tahoma, sans-serif;
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2.5rem;
  
  /* Border Radius */
  --radius-sm: 3px;
  --radius-md: 6px;
  --radius-lg: 12px;
  
  /* Shadows */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 10px 20px rgba(0, 0, 0, 0.1), 0 3px 6px rgba(0, 0, 0, 0.05);
  
  /* Transitions */
  --transition-fast: 0.2s ease;
  --transition-medium: 0.3s ease;
  --transition-slow: 0.5s ease;
}

/* ---------- Typography ---------- */
body {
  font-family: var(--font-body);
  font-size: 16px;
  line-height: 1.6;
  color: var(--text-dark);
  background-color: var(--background);
  /* Parchment-like texture */
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23d4a76a' fill-opacity='0.1' fill-rule='evenodd'/%3E%3C/svg%3E");
  background-attachment: fixed;
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-heading);
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: var(--spacing-md);
  color: var(--primary);
}

h1 {
  font-size: 2rem;
  margin-bottom: var(--spacing-lg);
  position: relative;
}

h1::after {
  content: '';
  display: block;
  width: 60px;
  height: 3px;
  background-color: var(--secondary);
  position: absolute;
  bottom: -10px;
  left: 0;
}

h2 {
  font-size: 1.75rem;
}

h3 {
  font-size: 1.5rem;
}

h4 {
  font-size: 1.25rem;
}

p {
  margin-bottom: var(--spacing-md);
}

a {
  color: var(--accent);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover, a:focus {
  color: var(--accent-light);
  text-decoration: underline;
}

/* ---------- Layout: CSS Grid for Main Structure ---------- */
.site-container {
  display: grid;
  grid-template-areas:
    "header"
    "main"
    "sidebar"
    "footer";
  min-height: 100vh;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

.site-header {
  grid-area: header;
  padding: var(--spacing-md) 0;
  border-bottom: 1px solid var(--secondary);
}

.site-main {
  grid-area: main;
  padding: var(--spacing-lg) 0;
}

.site-sidebar {
  grid-area: sidebar;
  padding: var(--spacing-lg) 0;
}

.site-footer {
  grid-area: footer;
  padding: var(--spacing-lg) 0;
  border-top: 1px solid var(--secondary);
  text-align: center;
  margin-top: auto;
  background-color: var(--secondary-light);
}

/* ---------- Header & Navigation ---------- */
.site-header {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.site-logo {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.site-logo img {
  height: 50px;
  margin-right: var(--spacing-sm);
}

.site-title {
  font-size: 1.5rem;
  margin: 0;
}

.site-title::after {
  display: none;
}

.site-navigation {
  width: 100%;
}

.nav-toggle {
  display: block;
  width: 100%;
  padding: var(--spacing-sm);
  background-color: var(--primary);
  color: var(--text-light);
  border: none;
  border-radius: var(--radius-sm);
  cursor: pointer;
  font-family: var(--font-body);
  font-size: 1rem;
  text-align: center;
}

.nav-menu {
  display: none;
  list-style: none;
  margin-top: var(--spacing-sm);
  width: 100%;
}

.nav-menu.active {
  display: block;
}

.nav-menu li {
  margin-bottom: var(--spacing-xs);
}

.nav-menu a {
  display: block;
  padding: var(--spacing-sm);
  color: var(--text-dark);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
  text-align: center;
  font-weight: 500;
  position: relative;
  overflow: hidden;
}

.nav-menu a::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.7s;
}

.nav-menu a:hover, .nav-menu a:focus, .nav-menu a.active {
  background-color: var(--secondary);
  color: var(--primary);
  text-decoration: none;
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.nav-menu a:hover::before {
  left: 100%;
}

/* ---------- Blog Cards: Flexbox Layout ---------- */
.blog-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.blog-card {
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: var(--radius-md);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: transform var(--transition-medium), box-shadow var(--transition-medium);
  position: relative;
}

.blog-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.blog-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background-color: var(--primary);
}

.blog-card-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.blog-card-content {
  padding: var(--spacing-md);
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.blog-card-title {
  margin-bottom: var(--spacing-sm);
  font-size: 1.25rem;
}

.blog-card-meta {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-md);
  font-size: 0.875rem;
  color: #666;
}

.blog-card-date {
  margin-right: var(--spacing-md);
}

.blog-card-author {
  display: flex;
  align-items: center;
}

.blog-card-author img {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  margin-right: var(--spacing-xs);
}

.blog-card-excerpt {
  margin-bottom: var(--spacing-md);
  flex-grow: 1;
}

.blog-card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}

.blog-card-categories {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
}

.blog-card-category {
  font-size: 0.75rem;
  padding: 2px 8px;
  background-color: var(--secondary-light);
  border-radius: 20px;
  color: var(--primary);
}

.blog-card-link {
  display: inline-block;
  padding: var(--spacing-xs) var(--spacing-sm);
  background-color: var(--primary);
  color: var(--text-light);
  border-radius: var(--radius-sm);
  font-size: 0.875rem;
  transition: background-color var(--transition-fast);
}

.blog-card-link:hover, .blog-card-link:focus {
  background-color: var(--primary-light);
  color: var(--text-light);
  text-decoration: none;
}

/* ---------- Sidebar Widgets ---------- */
.widget {
  background-color: #fff;
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
}

.widget-title {
  font-size: 1.25rem;
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-xs);
  border-bottom: 2px solid var(--secondary);
}

.widget-content ul {
  list-style: none;
}

.widget-content li {
  margin-bottom: var(--spacing-sm);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--secondary-light);
}

.widget-content li:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

/* ---------- Buttons & Form Elements ---------- */
.button {
  display: inline-block;
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--primary);
  color: var(--text-light);
  border: none;
  border-radius: var(--radius-sm);
  cursor: pointer;
  font-family: var(--font-body);
  font-size: 1rem;
  text-align: center;
  transition: background-color var(--transition-fast);
}

.button:hover, .button:focus {
  background-color: var(--primary-light);
  color: var(--text-light);
  text-decoration: none;
}

.button-secondary {
  background-color: var(--secondary);
  color: var(--text-dark);
}

.button-secondary:hover, .button-secondary:focus {
  background-color: var(--secondary-light);
  color: var(--text-dark);
}

input, textarea, select {
  width: 100%;
  padding: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
  border: 1px solid #ddd;
  border-radius: var(--radius-sm);
  font-family: var(--font-body);
  font-size: 1rem;
}

input:focus, textarea:focus, select:focus {
  outline: none;
  border-color: var(--accent);
  box-shadow: 0 0 0 2px rgba(69, 123, 157, 0.2);
}

/* ---------- Utility Classes ---------- */
.text-center {
  text-align: center;
}

.mb-0 {
  margin-bottom: 0;
}

.mb-1 {
  margin-bottom: var(--spacing-sm);
}

.mb-2 {
  margin-bottom: var(--spacing-md);
}

.mb-3 {
  margin-bottom: var(--spacing-lg);
}

.mt-0 {
  margin-top: 0;
}

.mt-1 {
  margin-top: var(--spacing-sm);
}

.mt-2 {
  margin-top: var(--spacing-md);
}

.mt-3 {
  margin-top: var(--spacing-lg);
}

/* ---------- Responsive Breakpoints ---------- */
/* Tablet (768px and up) */
@media (min-width: 768px) {
  body {
    font-size: 17px;
  }
  
  h1 {
    font-size: 2.5rem;
  }
  
  h2 {
    font-size: 2rem;
  }
  
  h3 {
    font-size: 1.75rem;
  }
  
  .site-container {
    grid-template-areas:
      "header header"
      "main sidebar"
      "footer footer";
    grid-template-columns: 2fr 1fr;
    grid-gap: var(--spacing-lg);
    padding: 0 var(--spacing-lg);
  }
  
  .site-header {
    flex-direction: row;
    justify-content: space-between;
  }
  
  .site-logo {
    margin-bottom: 0;
  }
  
  .nav-toggle {
    display: none;
  }
  
  .nav-menu {
    display: flex;
    margin-top: 0;
    justify-content: flex-end;
  }
  
  .nav-menu li {
    margin-bottom: 0;
    margin-left: var(--spacing-sm);
  }
  
  .blog-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-lg);
  }
}

/* Desktop (992px and up) */
@media (min-width: 992px) {
  .site-container {
    padding: 0 var(--spacing-xl);
  }
  
  .blog-container {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .site-title {
    font-size: 2rem;
  }
}

/* Large Desktop (1200px and up) */
@media (min-width: 1200px) {
  .site-container {
    grid-template-columns: 3fr 1fr;
  }
}

/* ---------- JavaScript Hooks ---------- */
/* For the mobile navigation toggle */
.js-nav-toggle {
  cursor: pointer;
}

/* For the mobile navigation menu */
.js-nav-menu {
  transition: max-height var(--transition-medium);
  max-height: 0;
  overflow: hidden;
}

.js-nav-menu.active {
  max-height: 500px;
}

/* ---------- Print Styles ---------- */
@media print {
  body {
    background: none;
    color: black;
    font-size: 12pt;
  }
  
  .site-container {
    display: block;
    max-width: 100%;
  }
  
  .site-navigation, .site-sidebar, .blog-card-link {
    display: none;
  }
  
  a {
    color: black;
    text-decoration: underline;
  }
  
  h1, h2, h3, h4, h5, h6 {
    color: black;
    page-break-after: avoid;
  }
  
  img {
    max-width: 100% !important;
  }
  
  p, h2, h3 {
    orphans: 3;
    widows: 3;
  }
}
