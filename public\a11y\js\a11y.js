// Accessibility features
document.addEventListener('DOMContentLoaded', function() {
    // Toggle high contrast mode
    const a11yToggle = document.getElementById('a11y-toggle');
    if (a11yToggle) {
        a11yToggle.addEventListener('click', function() {
            document.body.classList.toggle('high-contrast');
            
            // Toggle the accessibility stylesheet
            const a11yStylesheet = document.querySelector('link[href="css/accessibility.css"]');
            if (a11yStylesheet) {
                a11yStylesheet.disabled = !a11yStylesheet.disabled;
            }
        });
    }
    
    // Font size controls
    const fontIncrease = document.getElementById('font-increase');
    const fontDecrease = document.getElementById('font-decrease');
    
    if (fontIncrease && fontDecrease) {
        fontIncrease.addEventListener('click', function() {
            if (document.body.classList.contains('extra-large-text')) {
                return;
            } else if (document.body.classList.contains('large-text')) {
                document.body.classList.remove('large-text');
                document.body.classList.add('extra-large-text');
            } else {
                document.body.classList.add('large-text');
            }
        });
        
        fontDecrease.addEventListener('click', function() {
            if (document.body.classList.contains('large-text')) {
                document.body.classList.remove('large-text');
            } else if (document.body.classList.contains('extra-large-text')) {
                document.body.classList.remove('extra-large-text');
                document.body.classList.add('large-text');
            }
        });
    }
});