<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Love Thy Neighbor WNC</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary: #2a6a3c;
            --secondary: #5aa76d;
            --accent: #e63946;
            --light: #f1faee;
            --dark: #1d3557;
            --neutral: #a8dadc;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background-color: #f9f9f9;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            width: 90%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 15px;
        }
        
        /* Header Styles */
        header {
            background-color: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 0;
        }
        
        .logo {
            display: flex;
            align-items: center;
        }
        
        .logo-placeholder {
            width: 60px;
            height: 60px;
            background-color: var(--primary);
            border-radius: 5px;
            margin-right: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        .org-name {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary);
        }
        
        nav ul {
            display: flex;
            list-style: none;
        }
        
        nav li {
            margin-left: 1.5rem;
        }
        
        nav a {
            text-decoration: none;
            color: var(--dark);
            font-weight: 500;
            transition: color 0.3s;
        }
        
        nav a:hover {
            color: var(--secondary);
        }
        
        /* Hero Section */
        .hero {
            background: linear-gradient(rgba(0,0,0,0.6), rgba(0,0,0,0.6)), url('https://images.unsplash.com/photo-1519834064978-9b52a4a97b53?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80');
            background-size: cover;
            background-position: center;
            color: white;
            padding: 5rem 0;
            text-align: center;
        }
        
        .hero h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }
        
        .hero p {
            font-size: 1.2rem;
            max-width: 800px;
            margin: 0 auto 2rem;
        }
        
        .cta-buttons {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-top: 2rem;
        }
        
        .btn {
            padding: 0.8rem 1.5rem;
            border-radius: 4px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background-color: var(--accent);
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #c1121f;
        }
        
        .btn-secondary {
            background-color: transparent;
            border: 2px solid white;
            color: white;
        }
        
        .btn-secondary:hover {
            background-color: white;
            color: var(--dark);
        }
        
        /* Section Styles */
        section {
            padding: 4rem 0;
        }
        
        .section-title {
            text-align: center;
            margin-bottom: 3rem;
            color: var(--primary);
        }
        
        .mission {
            background-color: white;
            text-align: center;
        }
        
        .mission-statement {
            max-width: 800px;
            margin: 0 auto;
            font-size: 1.1rem;
            line-height: 1.8;
        }
        
        /* Yard Signs Section */
        .yard-signs {
            background-color: var(--light);
        }
        
        .campaign-container {
            display: flex;
            flex-wrap: wrap;
            gap: 2rem;
            justify-content: center;
        }
        
        .campaign-card {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.3s;
            width: 300px;
            text-align: center;
        }
        
        .campaign-card:hover {
            transform: translateY(-5px);
        }
        
        .card-img {
            height: 180px;
            background-color: var(--neutral);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--dark);
            font-weight: bold;
        }
        
        .card-content {
            padding: 1.5rem;
        }
        
        .card-content h3 {
            color: var(--primary);
            margin-bottom: 0.8rem;
        }
        
        .card-content p {
            margin-bottom: 1.2rem;
        }
        
        /* Partners Section */
        .partners {
            background-color: white;
        }
        
        .partners-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }
        
        .partner-card {
            text-align: center;
            padding: 2rem;
            border: 1px solid #eee;
            border-radius: 8px;
        }
        
        .partner-logo {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            background-color: #ddd;
            margin: 0 auto 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #777;
        }
        
        .social-links {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .social-icon {
            width: 40px;
            height: 40px;
            background-color: var(--primary);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
        }
        
        /* Mutual Aid Section */
        .mutual-aid {
            background-color: var(--light);
        }
        
        .aid-opportunities {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }
        
        .aid-card {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            padding: 1.5rem;
        }
        
        .aid-card h3 {
            color: var(--primary);
            margin-bottom: 1rem;
        }
        
        /* Footer */
        footer {
            background-color: var(--dark);
            color: white;
            padding: 3rem 0 1.5rem;
        }
        
        .footer-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }
        
        .footer-column h3 {
            margin-bottom: 1.2rem;
            font-size: 1.2rem;
        }
        
        .footer-column ul {
            list-style: none;
        }
        
        .footer-column li {
            margin-bottom: 0.8rem;
        }
        
        .footer-column a {
            color: #ddd;
            text-decoration: none;
        }
        
        .footer-column a:hover {
            color: white;
            text-decoration: underline;
        }
        
        .copyright {
            text-align: center;
            padding-top: 1.5rem;
            border-top: 1px solid rgba(255,255,255,0.1);
            font-size: 0.9rem;
            color: #aaa;
        }
        
        /* Language Switcher */
        .language-switcher {
            position: relative;
            display: inline-block;
            margin-left: 1rem;
        }

        .language-dropdown {
            background-color: var(--primary);
            color: white;
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .language-dropdown:hover {
            background-color: var(--secondary);
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 10% auto;
            padding: 2rem;
            border-radius: 8px;
            width: 90%;
            max-width: 500px;
            position: relative;
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            position: absolute;
            top: 10px;
            right: 20px;
        }

        .close:hover,
        .close:focus {
            color: #000;
        }

        .modal h2 {
            color: var(--primary);
            margin-bottom: 1rem;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 0.8rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
        }

        .form-group input:focus {
            outline: none;
            border-color: var(--primary);
        }

        /* Fourth Amendment Resources */
        .fourth-amendment {
            background-color: white;
        }

        .resources-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
        }

        .resource-card {
            background: var(--light);
            border-radius: 8px;
            padding: 1.5rem;
            text-align: center;
            transition: transform 0.3s;
            border: 2px solid transparent;
        }

        .resource-card:hover {
            transform: translateY(-3px);
            border-color: var(--primary);
        }

        .resource-icon {
            width: 60px;
            height: 60px;
            background-color: var(--primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            color: white;
            font-size: 1.5rem;
        }

        .resource-card h3 {
            color: var(--primary);
            margin-bottom: 1rem;
        }

        .resource-card p {
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }

        /* Email Alert Button */
        .email-alert-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background-color: var(--accent);
            color: white;
            border: none;
            border-radius: 50px;
            padding: 1rem 1.5rem;
            font-weight: 600;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            transition: all 0.3s;
            z-index: 999;
        }

        .email-alert-btn:hover {
            background-color: #c1121f;
            transform: translateY(-2px);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .header-container {
                flex-direction: column;
            }

            nav ul {
                margin-top: 1rem;
                flex-wrap: wrap;
                justify-content: center;
            }

            nav li {
                margin: 0 0.5rem 0.5rem;
            }

            .language-switcher {
                margin-left: 0;
                margin-top: 1rem;
            }

            .cta-buttons {
                flex-direction: column;
                align-items: center;
            }

            .btn {
                width: 100%;
                text-align: center;
                margin-bottom: 0.5rem;
            }

            .modal-content {
                margin: 20% auto;
                width: 95%;
            }

            .email-alert-btn {
                bottom: 10px;
                right: 10px;
                padding: 0.8rem 1.2rem;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header>
        <div class="container header-container">
            <div class="logo">
                <div class="logo-placeholder">LTN</div>
                <div class="org-name">Love Thy Neighbor WNC</div>
            </div>
            
            <nav>
                <ul>
                    <li><a href="#mission">Mission</a></li>
                    <li><a href="#fourth-amendment">Know Your Rights</a></li>
                    <li><a href="#yard-signs">Yard Signs</a></li>
                    <li><a href="#partners">Partners</a></li>
                    <li><a href="#mutual-aid">Mutual Aid</a></li>
                    <li><a href="#contact">Contact</a></li>
                </ul>
                <div class="language-switcher">
                    <select class="language-dropdown" id="languageSelect">
                        <option value="en">English</option>
                        <option value="es">Español</option>
                    </select>
                </div>
            </nav>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <h1>Love Thy Neighbor Western North Carolina</h1>
            <p>Standing with our immigrant neighbors through faith, advocacy, and community support</p>
            <div class="cta-buttons">
                <a href="#yard-signs" class="btn btn-primary">Get a Yard Sign</a>
                <a href="#fourth-amendment" class="btn btn-secondary">Know Your Rights</a>
                <button onclick="openEmailModal()" class="btn btn-secondary" style="border: 2px solid white; background: transparent;">Get Email Alerts</button>
            </div>
        </div>
    </section>

    <!-- Mission Section -->
    <section id="mission" class="mission">
        <div class="container">
            <h2 class="section-title">Our Mission</h2>
            <div class="mission-statement">
                <p>Love Thy Neighbor WNC is a faith-based advocacy organization dedicated to supporting immigrant rights and fostering inclusive communities throughout Western North Carolina. We believe in extending radical hospitality and standing in solidarity with our immigrant neighbors, regardless of status.</p>
                <p>Through education, advocacy, and mutual aid, we work to create a region where all people are treated with dignity, respect, and compassion.</p>
            </div>
        </div>
    </section>

    <!-- Fourth Amendment Resources Section -->
    <section id="fourth-amendment" class="fourth-amendment">
        <div class="container">
            <h2 class="section-title">Know Your Rights - Fourth Amendment Resources</h2>
            <div class="resources-grid">
                <div class="resource-card">
                    <div class="resource-icon">
                        <i class="fas fa-building"></i>
                    </div>
                    <h3>Fourth Amendment at Workplaces</h3>
                    <p>Understand your constitutional rights regarding searches and privacy in workplace settings. Learn what employers can and cannot do.</p>
                    <a href="#" class="btn btn-primary" onclick="openWorkplaceInfo()">Learn More</a>
                </div>

                <div class="resource-card">
                    <div class="resource-icon">
                        <i class="fas fa-school"></i>
                    </div>
                    <h3>Fourth Amendment for Schools</h3>
                    <p>Know your rights and your children's rights in educational settings. Information for students, parents, and educators.</p>
                    <a href="#" class="btn btn-primary" onclick="openSchoolInfo()">Learn More</a>
                </div>

                <div class="resource-card">
                    <div class="resource-icon">
                        <i class="fas fa-church"></i>
                    </div>
                    <h3>Fourth Amendment for Churches</h3>
                    <p>Religious institutions and constitutional protections. Understanding sanctuary policies and religious freedom rights.</p>
                    <a href="#" class="btn btn-primary" onclick="openChurchInfo()">Learn More</a>
                </div>

                <div class="resource-card">
                    <div class="resource-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <h3>If You See A Raid</h3>
                    <p>Emergency response guide for witnessing immigration enforcement actions. Know how to help while staying safe.</p>
                    <a href="#" class="btn btn-primary" onclick="openRaidInfo()">Learn More</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Yard Signs Section -->
    <section id="yard-signs" class="yard-signs">
        <div class="container">
            <h2 class="section-title">Yard Sign Visibility Campaign</h2>
            <div class="campaign-container">
                <div class="campaign-card">
                    <div class="card-img">Yard Sign Image</div>
                    <div class="card-content">
                        <h3>Show Your Support</h3>
                        <p>Display a "Love Thy Neighbor" yard sign to show your commitment to inclusive communities.</p>
                        <a href="#" class="btn btn-primary">Purchase Sign</a>
                    </div>
                </div>
                
                <div class="campaign-card">
                    <div class="card-img">Know Your Rights</div>
                    <div class="card-content">
                        <h3>Know Your Rights</h3>
                        <p>Access resources to understand and protect your rights in various situations.</p>
                        <a href="#" class="btn btn-primary">View Resources</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Partners Section -->
    <section id="partners" class="partners">
        <div class="container">
            <h2 class="section-title">Our Partners</h2>
            <div class="partners-grid">
                <div class="partner-card">
                    <div class="partner-logo">Partner 1 Logo</div>
                    <h3>Organization One</h3>
                    <p>Description of the first partner organization and their role in the community.</p>
                    <p>Contact: <EMAIL></p>
                    <div class="social-links">
                        <a href="#" class="social-icon"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                
                <div class="partner-card">
                    <div class="partner-logo">Partner 2 Logo</div>
                    <h3>Organization Two</h3>
                    <p>Description of the second partner organization and their role in the community.</p>
                    <p>Contact: <EMAIL></p>
                    <div class="social-links">
                        <a href="#" class="social-icon"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Mutual Aid Section -->
    <section id="mutual-aid" class="mutual-aid">
        <div class="container">
            <h2 class="section-title">Mutual Aid Opportunities</h2>
            <div class="aid-opportunities">
                <div class="aid-card">
                    <h3>Emergency Support Fund</h3>
                    <p>Help community members facing crises with immediate financial assistance.</p>
                    <a href="#" class="btn btn-primary">Donate Now</a>
                </div>
                
                <div class="aid-card">
                    <h3>Community Sponsorship</h3>
                    <p>Support newly arrived families with housing, supplies, and integration assistance.</p>
                    <a href="#" class="btn btn-primary">Learn More</a>
                </div>
                
                <div class="aid-card">
                    <h3>Legal Defense Fund</h3>
                    <p>Help provide legal representation for immigrants facing deportation proceedings.</p>
                    <a href="#" class="btn btn-primary">Contribute</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer id="contact">
        <div class="container">
            <div class="footer-grid">
                <div class="footer-column">
                    <h3>Love Thy Neighbor WNC</h3>
                    <p>Faith-based advocacy for immigrant rights and justice in Western North Carolina.</p>
                </div>
                
                <div class="footer-column">
                    <h3>Contact Us</h3>
                    <p>Email: <EMAIL></p>
                    <p>Phone: (*************</p>
                </div>
                
                <div class="footer-column">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="#mission">Our Mission</a></li>
                        <li><a href="#fourth-amendment">Know Your Rights</a></li>
                        <li><a href="#yard-signs">Yard Signs</a></li>
                        <li><a href="#partners">Partners</a></li>
                        <li><a href="#mutual-aid">Mutual Aid</a></li>
                    </ul>
                </div>

                <div class="footer-column">
                    <h3>Fourth Amendment Resources</h3>
                    <ul>
                        <li><a href="#" onclick="openWorkplaceInfo()">Workplaces</a></li>
                        <li><a href="#" onclick="openSchoolInfo()">Schools</a></li>
                        <li><a href="#" onclick="openChurchInfo()">Churches</a></li>
                        <li><a href="#" onclick="openRaidInfo()">If You See A Raid</a></li>
                        <li><a href="#" onclick="openEmailModal()">Email Alerts</a></li>
                    </ul>
                </div>
            </div>
            
            
            <div class="copyright">
                <p>&copy; 2023 Love Thy Neighbor WNC. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Email Alert Button -->
    <button class="email-alert-btn" onclick="openEmailModal()">
        <i class="fas fa-envelope"></i> Email Alerts
    </button>

    <!-- Email Signup Modal -->
    <div id="emailModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeEmailModal()">&times;</span>
            <h2>Sign Up for Email Alerts</h2>
            <p>Stay informed about immigration rights, community events, and urgent alerts in Western North Carolina.</p>
            <form id="emailSignupForm" onsubmit="submitEmailForm(event)">
                <div class="form-group">
                    <label for="firstName">First Name:</label>
                    <input type="text" id="firstName" name="firstName" required>
                </div>
                <div class="form-group">
                    <label for="lastName">Last Name:</label>
                    <input type="text" id="lastName" name="lastName" required>
                </div>
                <div class="form-group">
                    <label for="email">Email Address:</label>
                    <input type="email" id="email" name="email" required>
                </div>
                <div class="form-group">
                    <label for="zipCode">Zip Code:</label>
                    <input type="text" id="zipCode" name="zipCode" placeholder="Optional">
                </div>
                <button type="submit" class="btn btn-primary" style="width: 100%;">Sign Up for Alerts</button>
            </form>
        </div>
    </div>

    <script>
        // Language Switcher
        document.getElementById('languageSelect').addEventListener('change', function() {
            const selectedLang = this.value;
            if (selectedLang === 'es') {
                translateToSpanish();
            } else {
                translateToEnglish();
            }
        });

        // Modal Functions
        function openEmailModal() {
            document.getElementById('emailModal').style.display = 'block';
        }

        function closeEmailModal() {
            document.getElementById('emailModal').style.display = 'none';
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('emailModal');
            if (event.target == modal) {
                modal.style.display = 'none';
            }
        }

        // Email Form Submission
        function submitEmailForm(event) {
            event.preventDefault();
            const formData = new FormData(event.target);
            const data = Object.fromEntries(formData);

            // Here you would typically send the data to your server
            console.log('Email signup data:', data);
            alert('Thank you for signing up! You will receive email alerts about important community updates.');
            closeEmailModal();
            event.target.reset();
        }

        // Fourth Amendment Resource Functions
        function openWorkplaceInfo() {
            alert('Workplace Rights Information:\n\n• Employers generally cannot search personal belongings without consent\n• You have the right to refuse unreasonable searches\n• Know your company policies regarding privacy\n• Contact legal aid if you believe your rights were violated');
        }

        function openSchoolInfo() {
            alert('School Rights Information:\n\n• Students have limited Fourth Amendment protections in schools\n• Schools can search lockers and bags with reasonable suspicion\n• Parents have rights regarding their children\'s privacy\n• Know your school district\'s search policies');
        }

        function openChurchInfo() {
            alert('Church Rights Information:\n\n• Religious institutions have strong constitutional protections\n• Sanctuary policies provide some protection for immigrants\n• Know your congregation\'s policies and procedures\n• Religious freedom includes protection from unreasonable searches');
        }

        function openRaidInfo() {
            alert('If You See A Raid:\n\n• Stay calm and observe from a safe distance\n• Do not interfere with law enforcement\n• Document what you see (video/photos if safe)\n• Contact legal aid organizations immediately\n• Know emergency contact numbers\n• Provide support to affected families');
        }

        // Basic Spanish Translation (simplified for demo)
        function translateToSpanish() {
            // This is a simplified translation - in a real application, you'd want a more robust solution
            document.querySelector('.org-name').textContent = 'Ama a Tu Prójimo WNC';
            document.querySelector('.hero h1').textContent = 'Ama a Tu Prójimo Carolina del Norte Occidental';
            document.querySelector('.hero p').textContent = 'Apoyando a nuestros vecinos inmigrantes a través de la fe, la defensa y el apoyo comunitario';
        }

        function translateToEnglish() {
            document.querySelector('.org-name').textContent = 'Love Thy Neighbor WNC';
            document.querySelector('.hero h1').textContent = 'Love Thy Neighbor Western North Carolina';
            document.querySelector('.hero p').textContent = 'Standing with our immigrant neighbors through faith, advocacy, and community support';
        }
    </script>
</body>
</html>