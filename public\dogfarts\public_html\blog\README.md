# Dogfarts School Blog System

This is a simple blog system for the Dogfarts School of Witchcraft and Wizardry website. It allows for displaying blog posts from a MySQL database, sorted by date, with categories and search functionality.

## Features

- Display blog posts sorted by date (newest first)
- View individual blog posts
- Browse posts by category
- Search for posts
- Responsive design
- Secure database queries using prepared statements

## Setup Instructions

### 1. Database Setup

1. Make sure you have MySQL installed and running
2. Update the database credentials in `public_html/config/db.php` with your actual database information:
   ```php
   define('DB_HOST', 'localhost');
   define('DB_NAME', 'dogfarts_db');
   define('DB_USER', 'your_username');
   define('DB_PASS', 'your_password');
   ```
3. Run the database setup script by visiting:
   ```
   http://your-website.com/admin/setup_database.php?confirm=yes
   ```
   This will create the necessary database tables and populate them with sample data.

### 2. File Structure

The blog system uses the following file structure:

- `public_html/blog/index.php` - Main blog listing page
- `public_html/blog/post.php` - Individual post display
- `public_html/blog/category.php` - Category view
- `public_html/blog/search.php` - Search functionality
- `public_html/blog/sidebar.php` - Blog sidebar
- `public_html/blog/sidebar-categories.php` - Categories widget
- `public_html/assets/css/blog.css` - Blog-specific styles

### 3. Customization

You can customize the appearance of the blog by modifying the CSS in `public_html/assets/css/blog.css`.

## Usage

### Viewing the Blog

To view the blog, navigate to:
```
http://your-website.com/blog/
```

### Adding New Posts

In a real implementation, you would add an admin interface to manage posts. For now, you can add new posts directly to the database:

1. Add an author to the `authors` table if needed
2. Add a post to the `posts` table
3. Add categories to the `categories` table if needed
4. Link the post to categories using the `post_categories` table

## Security Considerations

- All database queries use prepared statements to prevent SQL injection
- User input is properly sanitized before display using `htmlspecialchars()`
- Error messages are generic to avoid exposing sensitive information

## Future Enhancements

- Admin interface for managing posts, categories, and authors
- Comment system
- Social sharing functionality
- Related posts feature
- Pagination for blog listings
- Image upload functionality

## Troubleshooting

If you encounter issues:

1. Check that your database credentials are correct
2. Ensure the database and tables exist
3. Check PHP error logs for specific error messages
4. Verify file permissions allow PHP to read and execute the files
