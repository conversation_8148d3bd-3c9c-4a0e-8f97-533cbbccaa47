/* 
 * Spellbook Generator Styles
 * 
 * CSS for the magical spellbook generator, stylized popup,
 * and wand cursor animation.
 */

/* ---------- Spellbook Button ---------- */
.spellbook-button {
  position: relative;
  padding: 12px 24px;
  background: linear-gradient(135deg, #8e3200, #a84a00);
  color: #f5f1e9;
  border: none;
  border-radius: 5px;
  font-family: 'Playfair Display', Georgia, serif;
  font-size: 1.1rem;
  cursor: pointer;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  overflow: hidden;
  z-index: 1;
}

.spellbook-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    rgba(255, 255, 255, 0), 
    rgba(255, 255, 255, 0.2), 
    rgba(255, 255, 255, 0));
  transition: left 0.7s ease;
  z-index: -1;
}

.spellbook-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
}

.spellbook-button:hover::before {
  left: 100%;
}

.spellbook-button:active {
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2);
}

/* ---------- Wand Cursor ---------- */
.wand-cursor {
  position: fixed;
  width: 40px;
  height: 40px;
  pointer-events: none;
  z-index: 9999;
  opacity: 0;
  transition: opacity 0.3s ease;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24'%3E%3Cpath fill='none' d='M0 0h24v24H0z'/%3E%3Cpath d='M19.88 18.47c.44-.7.7-1.51.7-2.39 0-2.49-2.01-4.5-4.5-4.5s-4.5 2.01-4.5 4.5 2.01 4.5 4.5 4.5c.88 0 1.69-.26 2.39-.7L21.58 23 23 21.58l-3.12-3.11zm-3.8.11c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5zm-.36-8.5c-.74.02-1.45.18-2.1.45l-.55-1.5-1.76-4.76-1.34 4.73-1.34 4.73-1.34-2.24-.37-.63-4.2-.11 3.63 2.64.99.72-1.14 3.99L11.69 15l.33-.24.33-.24 1.14 3.04L14.62 21l.68-2.38.23-.81h.01l.7-2.4.01-.06.01-.01z' fill='%238e3200'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  transform: translate(-50%, -50%) rotate(45deg);
  transform-origin: center;
}

.wand-cursor.active {
  opacity: 1;
  transform: translate(-50%, -50%) rotate(30deg) scale(1.2);
}

.wand-sparkle {
  position: absolute;
  width: 8px;
  height: 8px;
  background-color: #f9dc5c;
  border-radius: 50%;
  top: 0;
  left: 0;
  animation: sparkle-fade 1s ease-out forwards;
  box-shadow: 0 0 10px 2px rgba(249, 220, 92, 0.8);
}

@keyframes sparkle-fade {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  50% {
    transform: scale(1.5);
    opacity: 0.7;
  }
  100% {
    transform: scale(0);
    opacity: 0;
  }
}

/* ---------- Spell Popup ---------- */
.spell-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  opacity: 0;
  transition: opacity 0.4s ease;
}

.spell-popup-overlay.active {
  opacity: 1;
}

.spell-popup-content {
  position: relative;
  width: 90%;
  max-width: 500px;
  background-color: #f5f1e9;
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23d4a76a' fill-opacity='0.1' fill-rule='evenodd'/%3E%3C/svg%3E");
  border-radius: 8px;
  box-shadow: 0 5px 30px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  transform: scale(0.8);
  opacity: 0;
  transition: transform 0.4s ease, opacity 0.4s ease;
  border: 8px solid transparent;
  border-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='75' height='75'%3E%3Cg fill='none' stroke='%238e3200' stroke-width='2'%3E%3Cpath d='M1 1h73v73H1z'/%3E%3Cpath d='M8 8h59v59H8z'/%3E%3C/g%3E%3C/svg%3E") 25 stretch;
}

.spell-popup-content.active {
  transform: scale(1);
  opacity: 1;
}

.spell-popup-header {
  padding: 20px;
  text-align: center;
  border-bottom: 2px solid #d4a76a;
}

.spell-popup-header h3 {
  margin: 0;
  color: #8e3200;
  font-family: 'Playfair Display', Georgia, serif;
  font-size: 1.5rem;
  font-weight: 700;
}

.spell-popup-body {
  padding: 30px;
  text-align: center;
}

.spell-advice {
  font-family: 'Playfair Display', Georgia, serif;
  font-size: 1.2rem;
  line-height: 1.6;
  color: #2b2b2b;
  margin: 0;
  position: relative;
  font-style: italic;
}

.spell-advice::before,
.spell-advice::after {
  content: '"';
  font-size: 2rem;
  color: #d4a76a;
  position: absolute;
}

.spell-advice::before {
  top: -10px;
  left: -15px;
}

.spell-advice::after {
  bottom: -30px;
  right: -15px;
}

.spell-popup-footer {
  padding: 15px;
  text-align: center;
  border-top: 2px solid #d4a76a;
  font-family: 'Raleway', 'Segoe UI', Tahoma, sans-serif;
  font-size: 0.9rem;
  color: #8e3200;
}

.spell-popup-close {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 30px;
  height: 30px;
  background: #8e3200;
  color: #f5f1e9;
  border: none;
  border-radius: 50%;
  font-size: 1.5rem;
  line-height: 1;
  cursor: pointer;
  z-index: 10;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: background-color 0.3s ease, transform 0.3s ease;
}

.spell-popup-close:hover {
  background-color: #a84a00;
  transform: rotate(90deg);
}

/* Sparkle animation for popup */
.sparkle {
  position: absolute;
  background-color: #f9dc5c;
  border-radius: 50%;
  opacity: 0;
  animation: sparkle-animation 2s ease-in-out forwards;
}

@keyframes sparkle-animation {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  20% {
    opacity: 0.8;
  }
  50% {
    opacity: 0.4;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .spell-popup-content {
    width: 95%;
    max-width: 400px;
  }
  
  .spell-popup-header h3 {
    font-size: 1.3rem;
  }
  
  .spell-advice {
    font-size: 1.1rem;
  }
}

/* Hide default cursor when wand is active */
body.wand-active {
  cursor: none;
}
