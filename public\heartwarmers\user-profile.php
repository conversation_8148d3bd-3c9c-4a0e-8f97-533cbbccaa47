<?php
/**
 * User Profile page for Heartwarmers website
 */

// Include database functions
require_once 'php/includes/db.php';
require_once 'php/includes/functions.php';
require_once 'php/includes/user-functions.php';
require_once 'php/includes/sample-data.php';

// Get user slug from URL parameter
$userSlug = isset($_GET['slug']) ? sanitize_input($_GET['slug']) : '';

// Check if the slug parameter is actually a URL or contains 'user-profile.php'
// This prevents redirect loops
if (strpos($userSlug, 'user-profile.php') !== false || strpos($userSlug, '?') !== false || strpos($userSlug, '/') !== false) {
    // Invalid slug, reset it
    $userSlug = '';
}

// If no slug provided, check for ID parameter
if (empty($userSlug)) {
    $userId = isset($_GET['id']) ? intval($_GET['id']) : 0;

    // If no ID provided and user is logged in, show current user's profile
    if ($userId === 0 && is_logged_in()) {
        $currentUser = get_logged_in_user();
        $userId = $currentUser['id'];
    }

    // Get user by ID
    if ($userId > 0) {
        $user = get_user_by_id($userId);

        if ($user) {
            // Only redirect if we're not already in a redirect loop
            if (!isset($_GET['no_redirect'])) {
                // Use direct access to avoid redirection loops
                $_GET['slug'] = $user['slug'];
                $_GET['no_redirect'] = '1';
            } else {
                // If we're already in a redirect loop, just use the user data we have
                // No redirection needed
            }
        }
    }
} else {
    // Get user by slug
    $user = get_user_by_slug($userSlug);
}

// If user not found in database, use sample data
if (empty($user)) {
    // Sample user data for demonstration
    $user = [
        'id' => 1,
        'username' => 'April Chip',
        'slug' => 'april-chip',
        'email' => '<EMAIL>',
        'location' => 'Candler, North Carolina',
        'bio' => 'In this space, the user can customize their bios with sections of their choice. Such as \'About\', \'Frequent Questions\', \'Wishlist\', etc. The only limit is 500 words.',
        'profile_image' => 'assets/icons/user-avatar.png',
        'banner_image' => 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&h=400&q=80',
        'contact_info' => 'The best way to reach me is through email or by leaving a message here.',
        'donation_info' => 'If you would like to help, you can donate via PayPal or Venmo.',
        'created_at' => '2023-01-15 10:30:00'
    ];

    // Sample sections
    $sections = [
        [
            'id' => 1,
            'title' => 'About Me',
            'content' => 'I\'m currently experiencing homelessness in Western North Carolina. I\'m working hard to improve my situation and find stable housing.',
            'display_order' => 1,
            'is_visible' => 1
        ],
        [
            'id' => 2,
            'title' => 'My Story',
            'content' => 'I lost my housing six months ago due to a series of unfortunate events. I\'m currently staying in my car while looking for work and saving for a deposit on an apartment.',
            'display_order' => 2,
            'is_visible' => 1
        ],
        [
            'id' => 3,
            'title' => 'Frequently Asked Questions',
            'content' => 'Q: How can I help?\nA: The items on my wishlist would be most helpful right now, especially help with car repairs.\n\nQ: Are you looking for work?\nA: Yes, I\'m actively seeking kitchen work or other temporary gigs.',
            'display_order' => 3,
            'is_visible' => 1
        ]
    ];

    // Sample wishlist
    $wishlist = [
        [
            'id' => 1,
            'title' => 'Sleeper Van',
            'description' => 'A reliable van that I can sleep in would be life-changing. It would provide safer shelter than my current situation.',
            'priority' => 1,
            'status' => 'active',
            'image' => '',
            'price' => 3500.00,
            'url' => '',
            'created_at' => '2023-02-01 14:25:00'
        ],
        [
            'id' => 2,
            'title' => 'Temporary Paid Kitchen Gigs',
            'description' => 'I have 5+ years of kitchen experience and am looking for temporary work. Available immediately.',
            'priority' => 2,
            'status' => 'active',
            'image' => '',
            'price' => null,
            'url' => '',
            'created_at' => '2023-02-05 09:15:00'
        ],
        [
            'id' => 3,
            'title' => 'Help with brakework on my car',
            'description' => 'My car needs new brake pads and rotors. Parts cost around $150, but I also need help with installation.',
            'priority' => 1,
            'status' => 'active',
            'image' => '',
            'price' => 150.00,
            'url' => '',
            'created_at' => '2023-02-10 16:40:00'
        ]
    ];

    // Sample posts
    $posts = [
        [
            'id' => 1,
            'content' => 'Found a temporary kitchen job for the weekend! Every bit helps.',
            'image' => '',
            'is_pinned' => false,
            'created_at' => '2023-03-01 18:30:00'
        ],
        [
            'id' => 2,
            'content' => 'Thank you to everyone who has offered support. Your kindness means more than you know.',
            'image' => '',
            'is_pinned' => true,
            'created_at' => '2023-02-20 12:15:00'
        ]
    ];

    // Sample donations
    $donations = [
        [
            'id' => 1,
            'donor_name' => 'Anonymous',
            'amount' => 25.00,
            'message' => 'Hope this helps with the car repairs.',
            'is_anonymous' => true,
            'created_at' => '2023-03-05 10:20:00'
        ],
        [
            'id' => 2,
            'donor_name' => 'Sarah Johnson',
            'amount' => 50.00,
            'message' => 'Wishing you the best!',
            'is_anonymous' => false,
            'created_at' => '2023-03-02 14:45:00'
        ]
    ];
} else {
    // Get user sections from database
    $sections = get_user_sections($user['id']);

    // Get user wishlist from database
    $wishlist = get_user_wishlist($user['id']);

    // Get user posts from database
    $posts = get_user_posts($user['id'], 5);

    // Check if there was a database error and show helpful message
    if (empty($posts) && isset($_GET['db_error'])) {
        $dbErrorMessage = "Database needs to be updated for enhanced posting features. <a href='update_database.php'>Click here to update</a>";
    }

    // Get user donations from database
    $donations = get_user_donations($user['id'], 5);
}

// Generate QR code for profile
$qrCode = generate_profile_qr($user['slug']);

// Check if current user is viewing their own profile
$isOwnProfile = is_logged_in() && get_logged_in_user()['id'] == $user['id'];

// Handle success/error messages
$message = '';
$messageType = '';
if (isset($_GET['deleted']) && $_GET['deleted'] == '1') {
    $message = 'Section deleted successfully!';
    $messageType = 'success';
} elseif (isset($_GET['section_added']) && $_GET['section_added'] == '1') {
    $message = 'Section added successfully!';
    $messageType = 'success';
} elseif (isset($_GET['error']) && $_GET['error'] == 'delete_failed') {
    $message = 'Failed to delete section. Please try again.';
    $messageType = 'error';
}

// Set page variables
$pageTitle = $user['username'] . ' - Heartwarmers';
$pageDescription = 'User profile and wishlist for ' . $user['username'];
$currentPage = 'profile';
$pageStyles = ['css/profile.css', 'css/payment-icons.css'];
$pageScripts = ['https://cdn.tiny.cloud/1/1k1uybzjjjz7ot5pey6j8l352msuxhp1jzj583wroay17pue/tinymce/6/tinymce.min.js'];

// Include header
include_once 'templates/components/header.php';
?>

<div class="breadcrumb">
    <div class="container">
        <a href="index.php">Home</a> &gt;
        <span>Profile</span> &gt;
        <span><?php echo htmlspecialchars($user['username']); ?></span>
    </div>
</div>

<div class="user-profile">
    <div class="profile-header">
        <div class="container">
            <!-- Banner Section with Overlapping Elements -->
            <div class="banner-container" style="position: relative; margin-bottom: 40px;">
                <div class="banner" style="height: 200px; position: relative; overflow: hidden; border-radius: 12px; background-color: #3b82f6;">
                    <?php if (!empty($user['banner_image'])): ?>
                        <div class="banner-background" style="background-image: url('<?php echo htmlspecialchars($user['banner_image']); ?>'); width: 100%; height: 200px; background-size: cover; background-position: center; background-repeat: no-repeat; display: block; border-radius: 12px;"></div>
                    <?php else: ?>
                        <div class="banner-background" style="background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); width: 100%; height: 200px; display: block; border-radius: 12px;"></div>
                    <?php endif; ?>

                    <!-- Semi-transparent overlay for better text readability -->
                    <div style="position: absolute; bottom: 0; left: 0; right: 0; height: 100px; background: linear-gradient(transparent, rgba(0,0,0,0.3)); border-radius: 0 0 12px 12px;"></div>
                </div>

                <!-- Profile Picture - Elegantly Overlapping -->
                <div class="profile-picture" style="width: 120px; height: 120px; border-radius: 50%; overflow: hidden; border: 4px solid white; box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2); position: absolute; bottom: -30px; left: 30px; z-index: 10; background: white;">
                    <img src="<?php echo !empty($user['profile_image']) ? htmlspecialchars($user['profile_image']) : '/assets/icons/user-avatar.png'; ?>" alt="<?php echo htmlspecialchars($user['username']); ?>" class="profile-picture-image" style="width: 120px; height: 120px; object-fit: cover; display: block;">
                </div>

                <!-- Name and Info - Overlaying Banner -->
                <div style="position: absolute; bottom: 20px; left: 170px; right: 20px; color: white; text-shadow: 0 2px 4px rgba(0,0,0,0.5);">
                    <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 5px;">
                        <h1 class="username" style="margin: 0; font-size: 2rem; font-weight: 600; color: white; text-shadow: 0 2px 4px rgba(0,0,0,0.7);"><?php echo htmlspecialchars($user['username']); ?></h1>
                        <?php
                        $userType = $user['user_type'] ?? 'help_seeker';
                        $typeLabels = [
                            'help_seeker' => ['label' => 'Seeking Help', 'icon' => 'fas fa-heart', 'color' => '#ef4444'],
                            'business_organization' => ['label' => 'Organization', 'icon' => 'fas fa-building', 'color' => '#3b82f6'],
                            'volunteer_mutual_aid' => ['label' => 'Volunteer', 'icon' => 'fas fa-hands-helping', 'color' => '#10b981']
                        ];
                        $typeInfo = $typeLabels[$userType] ?? $typeLabels['help_seeker'];
                        ?>
                        <span class="user-type-badge" style="background-color: <?php echo $typeInfo['color']; ?>; color: white; padding: 4px 12px; border-radius: 20px; font-size: 0.875rem; font-weight: 500; display: flex; align-items: center; gap: 6px; box-shadow: 0 2px 4px rgba(0,0,0,0.3);">
                            <i class="<?php echo $typeInfo['icon']; ?>"></i>
                            <?php echo $typeInfo['label']; ?>
                        </span>
                    </div>
                    <p class="location" style="margin: 0; color: rgba(255,255,255,0.9); font-size: 1rem; display: flex; align-items: center; gap: 6px; text-shadow: 0 1px 2px rgba(0,0,0,0.5);">
                        <i class="fas fa-map-marker-alt" style="color: #fbbf24;"></i>
                        <?php echo htmlspecialchars($user['location']); ?>
                    </p>
                </div>
            </div>

            <!-- Profile Actions Section -->
            <div class="profile-info" style="padding: 0 20px; margin-bottom: 20px;">

                <div class="profile-actions">
                    <?php if ($isOwnProfile): ?>
                        <a href="./edit-profile.php" class="button btn-primary">Edit Profile</a>
                        <a href="./edit-wishlist.php" class="button btn-secondary">Manage Wishlist</a>
                    <?php else: ?>
                        <!-- Support options button temporarily hidden
                        <button class="button btn-primary" id="support-options-button">Support Options</button>
                        -->
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <?php if (!empty($message)): ?>
            <div class="alert alert-<?php echo $messageType; ?>">
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>

        <?php if (isset($dbErrorMessage)): ?>
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i>
                <?php echo $dbErrorMessage; ?>
            </div>
        <?php endif; ?>

        <div class="profile-content">
            <div class="profile-sidebar">
                <!-- Wishlist Section -->
                <div class="profile-section wishlist-section">
                    <div class="section-header">
                        <h2>Wishlist</h2>
                        <?php if ($isOwnProfile): ?>
                            <a href="edit-wishlist.php" class="edit-link"><i class="fas fa-edit"></i></a>
                        <?php endif; ?>
                    </div>

                    <?php if (!empty($wishlist)): ?>
                        <div class="wishlist-items">
                            <?php foreach ($wishlist as $item): ?>
                                <?php if ($item['status'] === 'active'): ?>
                                    <div class="wishlist-item priority-<?php echo $item['priority']; ?>">
                                        <?php if (!empty($item['image'])): ?>
                                            <div class="wishlist-item-image">
                                                <img src="<?php echo htmlspecialchars($item['image']); ?>" alt="<?php echo htmlspecialchars($item['title']); ?>">
                                            </div>
                                        <?php endif; ?>

                                        <div class="wishlist-item-content">
                                            <div class="wishlist-item-header">
                                                <h3><?php echo htmlspecialchars($item['title']); ?></h3>
                                                <?php if ($item['priority'] == 1): ?>
                                                    <span class="priority-badge high">High Priority</span>
                                                <?php elseif ($item['priority'] == 2): ?>
                                                    <span class="priority-badge medium">Medium Priority</span>
                                                <?php else: ?>
                                                    <span class="priority-badge low">Low Priority</span>
                                                <?php endif; ?>
                                            </div>

                                            <?php if (!empty($item['description'])): ?>
                                                <p class="wishlist-description"><?php echo htmlspecialchars($item['description']); ?></p>
                                            <?php endif; ?>

                                            <?php if (!empty($item['price'])): ?>
                                                <p class="wishlist-price">Estimated cost: $<?php echo number_format($item['price'], 2); ?></p>
                                            <?php endif; ?>

                                            <?php if (!empty($item['url'])): ?>
                                                <a href="<?php echo htmlspecialchars($item['url']); ?>" class="wishlist-link" target="_blank">View Item</a>
                                            <?php endif; ?>
                                        </div>

                                        <div class="wishlist-actions">
                                            <button class="fulfill-button" data-item-id="<?php echo $item['id']; ?>">I Can Help With This</button>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <p class="empty-state">No wishlist items yet.</p>
                    <?php endif; ?>
                </div>

                <!-- User Type Specific Actions -->
                <?php
                $userType = $user['user_type'] ?? 'help_seeker';
                if (!$isOwnProfile): // Only show for visitors, not profile owner
                ?>
                <div class="profile-section user-type-actions">
                    <?php if ($userType === 'business_organization'): ?>
                        <h2>Get Involved</h2>
                        <p>Interested in working with this organization?</p>
                        <div class="action-buttons">
                            <button class="btn-primary contact-button">Contact Organization</button>
                            <button class="btn-secondary learn-more-button">Learn More</button>
                        </div>
                    <?php elseif ($userType === 'volunteer_mutual_aid'): ?>
                        <h2>Connect</h2>
                        <p>Want to collaborate or request help?</p>
                        <div class="action-buttons">
                            <button class="btn-primary contact-button">Contact Volunteer</button>
                            <button class="btn-secondary view-services-button">View Services</button>
                        </div>
                    <?php else: // help_seeker ?>
                        <h2>How to Help</h2>
                        <p>Support <?php echo htmlspecialchars($user['username']); ?> in their journey</p>
                        <div class="action-buttons">
                            <button class="btn-primary contact-button">Send Message</button>
                            <button class="btn-secondary view-wishlist-button">View Wishlist</button>
                        </div>
                    <?php endif; ?>
                </div>
                <?php endif; ?>

                <!-- QR Code Section -->
                <div class="profile-section qr-section">
                    <h2>Share Profile</h2>
                    <div class="qr-code">
                        <img src="<?php echo $qrCode; ?>" alt="Profile QR Code">
                    </div>
                    <p class="profile-url"><?php echo htmlspecialchars($_SERVER['HTTP_HOST']); ?>/profile/<?php echo htmlspecialchars($user['slug']); ?></p>
                    <button class="copy-link-button" data-url="https://<?php echo htmlspecialchars($_SERVER['HTTP_HOST']); ?>/profile/<?php echo htmlspecialchars($user['slug']); ?>">Copy Link</button>
                </div>

                <!-- Donation Section -->
                <div class="profile-section donation-section">
                    <h2>Support <?php echo htmlspecialchars($user['username']); ?></h2>
                    <?php if (!empty($user['donation_info'])): ?>
                        <p><?php echo nl2br(htmlspecialchars($user['donation_info'])); ?></p>
                    <?php endif; ?>
                    <button class="donate-button" id="sidebar-donate-button">Donate</button>

                    <?php if (!empty($donations)): ?>
                        <div class="recent-donations">
                            <h3>Recent Donations</h3>
                            <ul class="donation-list">
                                <?php foreach ($donations as $donation): ?>
                                    <li class="donation-item">
                                        <span class="donation-amount">$<?php echo number_format($donation['amount'], 2); ?></span>
                                        <span class="donation-from">from <?php echo htmlspecialchars($donation['donor_name']); ?></span>
                                        <?php if (!empty($donation['message'])): ?>
                                            <p class="donation-message">"<?php echo htmlspecialchars($donation['message']); ?>"</p>
                                        <?php endif; ?>
                                        <span class="donation-time"><?php echo htmlspecialchars($donation['created_at']); ?></span>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Contact Section -->
                <?php if (!empty($user['contact_info'])): ?>
                    <div class="profile-section contact-section">
                        <h2>Contact Information</h2>
                        <p><?php echo nl2br(htmlspecialchars($user['contact_info'])); ?></p>
                        <button class="message-button" id="sidebar-message-button">Send Message</button>
                    </div>
                <?php endif; ?>
            </div>

            <div class="profile-main">
                <!-- Section Management for Profile Owner -->
                <?php if ($isOwnProfile): ?>
                    <div class="section-management">
                        <div class="section-management-header">
                            <h2>Your Profile Sections</h2>
                            <a href="add-section.php" class="btn-primary add-section-btn">
                                <i class="fas fa-plus"></i> Add New Section
                            </a>
                        </div>
                        <p class="section-help">Customize your profile by adding sections like "About Me", "FAQ", "My Story", etc. Click the edit icon on any section to modify it.</p>
                    </div>
                <?php endif; ?>

                <!-- User Sections -->
                <?php if (!empty($sections)): ?>
                    <?php foreach ($sections as $section): ?>
                        <?php if ($section['is_visible']): ?>
                            <div class="profile-section">
                                <div class="section-header">
                                    <h2><?php echo htmlspecialchars($section['title']); ?></h2>
                                    <?php if ($isOwnProfile): ?>
                                        <div class="section-actions">
                                            <a href="edit-section.php?id=<?php echo $section['id']; ?>" class="edit-link" title="Edit this section">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button class="toggle-visibility-btn" data-section-id="<?php echo $section['id']; ?>" title="Hide this section">
                                                <i class="fas fa-eye-slash"></i>
                                            </button>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="section-content">
                                    <?php
                                    // Check if content contains HTML tags
                                    if (strip_tags($section['content']) !== $section['content']) {
                                        // Content has HTML, display it directly (it's already sanitized)
                                        echo $section['content'];
                                    } else {
                                        // Plain text content, convert line breaks
                                        echo nl2br(htmlspecialchars($section['content']));
                                    }
                                    ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    <?php endforeach; ?>
                <?php else: ?>
                    <?php if ($isOwnProfile): ?>
                        <div class="empty-sections">
                            <div class="empty-sections-content">
                                <i class="fas fa-file-alt"></i>
                                <h3>No sections yet</h3>
                                <p>Start building your profile by adding your first section. You can add an "About Me" section, FAQ, or anything else you'd like to share.</p>
                                <a href="add-section.php" class="btn-primary">Add Your First Section</a>
                            </div>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>

                <!-- Testimonials Section -->
                <?php
                try {
                    require_once 'includes/testimonials-section.php';
                    display_testimonials_section($user, $isOwnProfile);
                } catch (Exception $e) {
                    // Fallback display if testimonials system isn't set up yet
                    ?>
                    <div class="profile-section testimonials-section" id="testimonials">
                        <div class="section-header">
                            <h2><i class="fas fa-comment-dots"></i> Testimonials</h2>
                        </div>
                        <div class="no-testimonials">
                            <div class="empty-state">
                                <i class="fas fa-comment-dots"></i>
                                <h3>Testimonials System</h3>
                                <p>The testimonials system is being set up. Please check back later or contact an administrator.</p>
                                <?php if ($isOwnProfile): ?>
                                    <a href="setup-testimonials.php" class="btn btn-primary">
                                        <i class="fas fa-cog"></i> Set Up Testimonials
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <style>
                        .testimonials-section .no-testimonials {
                            text-align: center;
                            padding: 3rem 1rem;
                            background: #f9fafb;
                            border-radius: 12px;
                            border: 2px dashed #d1d5db;
                        }
                        .testimonials-section .empty-state i {
                            font-size: 3rem;
                            color: #d1d5db;
                            margin-bottom: 1rem;
                        }
                        .testimonials-section .empty-state h3 {
                            color: #374151;
                            margin-bottom: 0.5rem;
                        }
                        .testimonials-section .empty-state p {
                            color: #6b7280;
                            margin-bottom: 1.5rem;
                        }
                    </style>
                    <?php
                }
                ?>

                <!-- User Posts -->
                <div class="profile-section posts-section">
                    <div class="section-header">
                        <h2>Updates</h2>
                    </div>

                    <?php if ($isOwnProfile): ?>
                    <!-- Quick Post Input -->
                    <div class="quick-post-input">
                        <div class="quick-post-content">
                            <img src="<?php echo !empty($user['profile_image']) ? htmlspecialchars($user['profile_image']) : '/assets/icons/user-avatar.png'; ?>" alt="Your avatar" class="quick-post-avatar" style="width: 40px; height: 40px; border-radius: 50%; object-fit: cover; flex-shrink: 0;">
                            <div class="quick-post-field" id="quick-post-trigger">
                                <input type="text" placeholder="Share an update, request, or thank you message..." readonly>
                                <button class="quick-post-btn">
                                    <i class="fas fa-edit"></i>
                                </button>
                            </div>
                        </div>
                        <div class="quick-post-actions">
                            <button class="quick-action" id="add-image-quick">
                                <i class="fas fa-image"></i> Photo
                            </button>
                            <button class="quick-action" id="add-video-quick">
                                <i class="fas fa-video"></i> Video
                            </button>
                            <button class="quick-action" id="add-link-quick">
                                <i class="fas fa-link"></i> Link
                            </button>
                        </div>
                    </div>

                    <div class="floating-action-button" id="new-post-button">
                        <i class="fas fa-plus"></i>
                    </div>
                    <?php endif; ?>

                    <?php if (!empty($posts)): ?>
                        <div class="posts-list">
                            <?php foreach ($posts as $post): ?>
                                <div class="post-item <?php echo $post['is_pinned'] ? 'pinned' : ''; ?>">
                                    <?php if ($post['is_pinned']): ?>
                                        <div class="pin-badge"><i class="fas fa-thumbtack"></i> Pinned</div>
                                    <?php endif; ?>

                                    <div class="post-content">
                                        <?php
                                        // Check if content contains HTML tags
                                        if (strip_tags($post['content']) !== $post['content']) {
                                            // Content has HTML, display it directly (it's already sanitized)
                                            echo $post['content'];
                                        } else {
                                            // Plain text content, convert line breaks
                                            echo nl2br(htmlspecialchars($post['content']));
                                        }
                                        ?>

                                        <?php if (!empty($post['image'])): ?>
                                            <div class="post-image">
                                                <img src="<?php echo htmlspecialchars($post['image']); ?>" alt="Post image">
                                            </div>
                                        <?php endif; ?>

                                        <?php if (isset($post['video_url']) && !empty($post['video_url'])): ?>
                                            <div class="post-video">
                                                <?php
                                                $videoUrl = $post['video_url'];
                                                $embedUrl = '';

                                                // Convert YouTube URLs to embed format
                                                if (strpos($videoUrl, 'youtube.com/watch?v=') !== false || strpos($videoUrl, 'youtu.be/') !== false) {
                                                    if (strpos($videoUrl, 'youtu.be/') !== false) {
                                                        $videoId = explode('youtu.be/', $videoUrl)[1];
                                                        $videoId = explode('?', $videoId)[0];
                                                    } else {
                                                        $videoId = explode('v=', $videoUrl)[1];
                                                        $videoId = explode('&', $videoId)[0];
                                                    }
                                                    $embedUrl = "https://www.youtube.com/embed/" . $videoId;
                                                }
                                                // Convert Vimeo URLs to embed format
                                                elseif (strpos($videoUrl, 'vimeo.com/') !== false) {
                                                    $videoId = explode('vimeo.com/', $videoUrl)[1];
                                                    $videoId = explode('?', $videoId)[0];
                                                    $embedUrl = "https://player.vimeo.com/video/" . $videoId;
                                                }

                                                if ($embedUrl): ?>
                                                    <iframe src="<?php echo htmlspecialchars($embedUrl); ?>" frameborder="0" allowfullscreen class="post-video-embed"></iframe>
                                                <?php else: ?>
                                                    <a href="<?php echo htmlspecialchars($videoUrl); ?>" target="_blank" class="video-link">
                                                        <i class="fas fa-play-circle"></i> Watch Video
                                                    </a>
                                                <?php endif; ?>
                                            </div>
                                        <?php endif; ?>

                                        <?php if (isset($post['link_url']) && !empty($post['link_url'])): ?>
                                            <div class="post-link">
                                                <a href="<?php echo htmlspecialchars($post['link_url']); ?>" target="_blank" class="link-preview">
                                                    <div class="link-preview-content">
                                                        <h5><?php echo htmlspecialchars(isset($post['link_title']) && $post['link_title'] ? $post['link_title'] : $post['link_url']); ?></h5>
                                                        <p><?php echo htmlspecialchars($post['link_url']); ?></p>
                                                        <i class="fas fa-external-link-alt"></i>
                                                    </div>
                                                </a>
                                            </div>
                                        <?php endif; ?>
                                    </div>

                                    <div class="post-footer">
                                        <span class="post-date"><?php echo htmlspecialchars($post['created_at']); ?></span>

                                        <?php if ($isOwnProfile): ?>
                                            <div class="post-actions">
                                                <?php if (!$post['is_pinned']): ?>
                                                    <button class="pin-post-button" data-post-id="<?php echo $post['id']; ?>"><i class="fas fa-thumbtack"></i> Pin</button>
                                                <?php else: ?>
                                                    <button class="unpin-post-button" data-post-id="<?php echo $post['id']; ?>"><i class="fas fa-thumbtack"></i> Unpin</button>
                                                <?php endif; ?>
                                                <button class="delete-post-button" data-post-id="<?php echo $post['id']; ?>"><i class="fas fa-trash"></i> Delete</button>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <p class="empty-state">No updates yet.</p>
                    <?php endif; ?>
                </div>

                <!-- Fulfilled Wishlist Items -->
                <?php
                $fulfilledItems = array_filter($wishlist ?? [], function($item) {
                    return $item['status'] === 'fulfilled';
                });

                if (!empty($fulfilledItems)):
                ?>
                    <div class="profile-section fulfilled-section">
                        <h2>Fulfilled Wishes</h2>
                        <div class="fulfilled-items">
                            <?php foreach ($fulfilledItems as $item): ?>
                                <div class="fulfilled-item">
                                    <h3><?php echo htmlspecialchars($item['title']); ?></h3>
                                    <p class="fulfilled-date">Fulfilled on <?php echo date('F j, Y', strtotime($item['fulfilled_at'])); ?></p>
                                    <p class="thank-you-message">Thank you to everyone who helped make this possible!</p>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Support Options Modal - Temporarily commented out
<div class="modal" id="support-options-modal">
    <div class="modal-backdrop"></div>
    <div class="modal-content">
        <button class="close-modal">&times;</button>
        <h3>Support <?php echo htmlspecialchars($user['username']); ?></h3>

        <div class="support-options">
            <div class="support-option" id="donate-option">
                <div class="support-option-icon">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <h4>Donate Money</h4>
                <p>Send a financial contribution to help with immediate needs.</p>
                <button class="button btn-outline" id="donate-money-button">Donate</button>
            </div>

            <div class="support-option" id="fulfill-option">
                <div class="support-option-icon">
                    <i class="fas fa-gift"></i>
                </div>
                <h4>Fulfill a Wish</h4>
                <p>Help by providing an item from the wishlist.</p>
                <button class="button btn-outline" id="fulfill-wish-button">View Wishlist</button>
            </div>

            <div class="support-option" id="message-option">
                <div class="support-option-icon">
                    <i class="fas fa-envelope"></i>
                </div>
                <h4>Send a Message</h4>
                <p>Reach out with words of encouragement or support.</p>
                <button class="button btn-outline" id="message-button">Message</button>
            </div>

            <div class="support-option" id="share-option">
                <div class="support-option-icon">
                    <i class="fas fa-share-alt"></i>
                </div>
                <h4>Share Profile</h4>
                <p>Help by sharing this profile with your network.</p>
                <button class="button btn-outline" id="share-button">Share</button>
            </div>
        </div>
    </div>
</div>
-->

<!-- Donation Modal - Temporarily commented out
<div class="modal" id="donation-modal">
    <div class="modal-backdrop"></div>
    <div class="modal-content">
        <button class="close-modal">&times;</button>
        <h3>Donate to <?php echo htmlspecialchars($user['username']); ?></h3>
        <div class="ko-fi-donation">
            <p>Support <?php echo htmlspecialchars($user['username']); ?> through Ko-Fi!</p>
            <p>Ko-Fi allows you to make one-time or recurring donations to support this user's needs and projects.</p>
            <div class="text-center">
                <button class="btn-primary" id="open-kofi-button" onclick="kofiWidgetOverlay.draw('aachips', {type: 'floating-chat'}); closeModals();">Open Ko-Fi Donation</button>
            </div>
            <div class="ko-fi-info">
                <p><strong>Why Ko-Fi?</strong></p>
                <ul>
                    <li>100% of your donation goes to the recipient</li>
                    <li>No platform fees for basic donations</li>
                    <li>Secure payment processing</li>
                    <li>Option for one-time or monthly support</li>
                </ul>
            </div>
        </div>
    </div>
</div>
-->

<!-- Message Modal - Temporarily commented out
<div class="modal" id="message-modal">
    <div class="modal-backdrop"></div>
    <div class="modal-content">
        <button class="close-modal">&times;</button>
        <h3>Send Message to <?php echo htmlspecialchars($user['username']); ?></h3>
        <form id="message-form">
            <div class="form-group">
                <label for="message-subject">Subject</label>
                <input type="text" id="message-subject" name="subject" required>
            </div>

            <div class="form-group">
                <label for="message-content">Message</label>
                <textarea id="message-content" name="message" rows="5" required></textarea>
            </div>

            <div class="form-group">
                <label for="message-email">Your Email</label>
                <input type="email" id="message-email" name="email" required>
                <p class="form-help">Your email will be shared with the recipient so they can reply to you.</p>
            </div>

            <div class="form-actions">
                <button type="submit" class="btn-primary">Send Message</button>
            </div>
        </form>
    </div>
</div>
-->

<!-- Share Modal - Temporarily commented out
<div class="modal" id="share-modal">
    <div class="modal-backdrop"></div>
    <div class="modal-content">
        <button class="close-modal">&times;</button>
        <h3>Share <?php echo htmlspecialchars($user['username']); ?>'s Profile</h3>

        <div class="qr-code-large">
            <img src="<?php echo $qrCode; ?>" alt="Profile QR Code">
        </div>

        <div class="share-link">
            <p>Profile Link:</p>
            <div class="copy-link-container">
                <input type="text" id="share-link-input" value="https://<?php echo htmlspecialchars($_SERVER['HTTP_HOST']); ?>/profile/<?php echo htmlspecialchars($user['slug']); ?>" readonly>
                <button id="copy-share-link" class="copy-button">Copy</button>
            </div>
        </div>

        <div class="social-share">
            <p>Share on Social Media:</p>
            <div class="social-buttons">
                <a href="https://www.facebook.com/sharer/sharer.php?u=https://<?php echo urlencode($_SERVER['HTTP_HOST'] . '/profile/' . $user['slug']); ?>" target="_blank" class="social-button facebook">
                    <i class="fab fa-facebook-f"></i>
                </a>
                <a href="https://twitter.com/intent/tweet?url=https://<?php echo urlencode($_SERVER['HTTP_HOST'] . '/profile/' . $user['slug']); ?>&text=<?php echo urlencode('Check out ' . $user['username'] . '\'s wishlist on Heartwarmers!'); ?>" target="_blank" class="social-button twitter">
                    <i class="fab fa-twitter"></i>
                </a>
                <a href="mailto:?subject=<?php echo urlencode($user['username'] . '\'s Heartwarmers Profile'); ?>&body=<?php echo urlencode('I thought you might be interested in helping ' . $user['username'] . ' through their Heartwarmers profile: https://' . $_SERVER['HTTP_HOST'] . '/profile/' . $user['slug']); ?>" class="social-button email">
                    <i class="fas fa-envelope"></i>
                </a>
            </div>
        </div>
    </div>
</div>
-->

<!-- New Post Modal -->
<?php if ($isOwnProfile): ?>
<div class="post-modal" id="new-post-modal">
    <div class="post-modal-content">
        <div class="post-modal-header">
            <h3 class="post-modal-title">Create New Update</h3>
            <button class="post-modal-close close-modal">&times;</button>
        </div>
        <div class="post-modal-body">
            <form id="new-post-form" enctype="multipart/form-data">
                <div class="form-group">
                    <label for="post-content">What's on your mind?</label>
                    <textarea id="post-content" name="content" rows="8" placeholder="Share an update, request, or thank you message..." required></textarea>
                    <p class="form-help">Use the rich text editor to format your post with bold, italic, links, and more.</p>
                </div>

                <div class="media-options">
                    <h4>Add Media</h4>
                    <div class="media-tabs">
                        <button type="button" class="media-tab active" data-tab="image">
                            <i class="fas fa-image"></i> Image
                        </button>
                        <button type="button" class="media-tab" data-tab="video">
                            <i class="fas fa-video"></i> Video
                        </button>
                        <button type="button" class="media-tab" data-tab="link">
                            <i class="fas fa-link"></i> Link
                        </button>
                    </div>

                    <div class="media-content">
                        <div class="media-panel active" id="image-panel">
                            <div class="form-group">
                                <label for="post-image" class="file-upload-label">
                                    <i class="fas fa-cloud-upload-alt"></i> Choose Image
                                </label>
                                <input type="file" id="post-image" name="image" accept="image/*" class="file-upload-input">
                                <p class="form-help">Upload an image to include with your post (JPG, PNG, GIF)</p>
                                <div id="image-preview" class="media-preview"></div>
                            </div>
                        </div>

                        <div class="media-panel" id="video-panel">
                            <div class="form-group">
                                <label for="video-url">Video URL</label>
                                <input type="url" id="video-url" name="video_url" placeholder="https://youtube.com/watch?v=... or https://vimeo.com/...">
                                <p class="form-help">Paste a YouTube, Vimeo, or other video URL to embed</p>
                                <div id="video-preview" class="media-preview"></div>
                            </div>
                        </div>

                        <div class="media-panel" id="link-panel">
                            <div class="form-group">
                                <label for="link-url">Link URL</label>
                                <input type="url" id="link-url" name="link_url" placeholder="https://example.com">
                                <p class="form-help">Add a link to share with your post</p>
                            </div>
                            <div class="form-group">
                                <label for="link-title">Link Title (Optional)</label>
                                <input type="text" id="link-title" name="link_title" placeholder="Custom title for the link">
                            </div>
                            <div id="link-preview" class="media-preview"></div>
                        </div>
                    </div>
                </div>

                <div class="post-options">
                    <h4>Post Options</h4>
                    <div class="form-group checkbox-group">
                        <label class="checkbox-label">
                            <input type="checkbox" name="pin" id="pin-post">
                            <span class="checkbox-text"><i class="fas fa-thumbtack"></i> Pin this update to the top of your profile</span>
                        </label>
                    </div>
                    <div class="form-group checkbox-group">
                        <label class="checkbox-label">
                            <input type="checkbox" name="allow_comments" id="allow-comments" checked>
                            <span class="checkbox-text"><i class="fas fa-comments"></i> Allow comments on this post</span>
                        </label>
                    </div>
                </div>

                <div class="post-modal-footer">
                    <button type="button" class="btn-secondary close-modal">Cancel</button>
                    <button type="submit" class="btn-primary">
                        <i class="fas fa-paper-plane"></i> Post Update
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php endif; ?>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize TinyMCE for post content
        tinymce.init({
            selector: '#post-content',
            height: 300,
            menubar: false,
            plugins: [
                'advlist', 'autolink', 'lists', 'link', 'charmap',
                'searchreplace', 'visualblocks', 'code',
                'insertdatetime', 'help', 'wordcount', 'emoticons'
            ],
            toolbar: 'undo redo | blocks | ' +
                'bold italic | alignleft aligncenter ' +
                'alignright | bullist numlist | ' +
                'link emoticons | removeformat | help',
            content_style: 'body { font-family: -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; font-size: 14px; line-height: 1.6; }',
            branding: false,
            promotion: false,
            setup: function (editor) {
                editor.on('change', function () {
                    editor.save();
                });
            }
        });

        // Support options functionality
        const supportOptionsButton = document.getElementById('support-options-button');
        const donateMoneyButton = document.getElementById('donate-money-button');
        const fulfillWishButton = document.getElementById('fulfill-wish-button');
        const messageButton = document.getElementById('message-button');
        const shareButton = document.getElementById('share-button');
        const newPostButton = document.getElementById('new-post-button');

        // Modal functionality
        const supportOptionsModal = document.getElementById('support-options-modal');
        const donationModal = document.getElementById('donation-modal');
        const messageModal = document.getElementById('message-modal');
        const shareModal = document.getElementById('share-modal');
        const newPostModal = document.getElementById('new-post-modal');

        const closeButtons = document.querySelectorAll('.close-modal');
        const modalBackdrops = document.querySelectorAll('.modal-backdrop');

        // Open modals
        function openModal(modal) {
            if (modal) {
                modal.classList.add('active');
                document.body.classList.add('modal-open');
            }
        }

        // Close all modals
        function closeModals() {
            document.querySelectorAll('.modal').forEach(modal => {
                modal.classList.remove('active');
            });
            document.body.classList.remove('modal-open');
        }

        // Support options modal
        if (supportOptionsButton) {
            supportOptionsButton.addEventListener('click', function() {
                openModal(supportOptionsModal);
            });
        }

        // Donation modal
        if (donateMoneyButton) {
            donateMoneyButton.addEventListener('click', function() {
                closeModals(); // Close the support options modal first
                openModal(donationModal);
            });
        }

        // Fulfill wish button - scrolls to wishlist section
        if (fulfillWishButton) {
            fulfillWishButton.addEventListener('click', function() {
                closeModals(); // Close the support options modal first
                const wishlistSection = document.querySelector('.wishlist-section');
                if (wishlistSection) {
                    wishlistSection.scrollIntoView({ behavior: 'smooth' });
                }
            });
        }

        // Message modal
        if (messageButton) {
            messageButton.addEventListener('click', function() {
                closeModals(); // Close the support options modal first
                openModal(messageModal);
            });
        }

        // Share modal
        if (shareButton) {
            shareButton.addEventListener('click', function() {
                closeModals(); // Close the support options modal first
                openModal(shareModal);
            });
        }

        // New post modal
        if (newPostButton) {
            newPostButton.addEventListener('click', function() {
                openModal(newPostModal);
            });
        }

        // Quick post input functionality
        const quickPostTrigger = document.getElementById('quick-post-trigger');
        const quickPostField = document.querySelector('.quick-post-field');
        const addImageQuick = document.getElementById('add-image-quick');
        const addVideoQuick = document.getElementById('add-video-quick');
        const addLinkQuick = document.getElementById('add-link-quick');

        if (quickPostTrigger) {
            quickPostTrigger.addEventListener('click', function() {
                openModal(newPostModal);
            });
        }

        if (quickPostField) {
            quickPostField.addEventListener('click', function() {
                openModal(newPostModal);
            });
        }

        if (addImageQuick) {
            addImageQuick.addEventListener('click', function() {
                openModal(newPostModal);
                // Switch to image tab
                setTimeout(() => {
                    const imageTab = document.querySelector('[data-tab="image"]');
                    if (imageTab) {
                        imageTab.click();
                    }
                }, 100);
            });
        }

        if (addVideoQuick) {
            addVideoQuick.addEventListener('click', function() {
                openModal(newPostModal);
                // Switch to video tab
                setTimeout(() => {
                    const videoTab = document.querySelector('[data-tab="video"]');
                    if (videoTab) {
                        videoTab.click();
                    }
                }, 100);
            });
        }

        if (addLinkQuick) {
            addLinkQuick.addEventListener('click', function() {
                openModal(newPostModal);
                // Switch to link tab
                setTimeout(() => {
                    const linkTab = document.querySelector('[data-tab="link"]');
                    if (linkTab) {
                        linkTab.click();
                    }
                }, 100);
            });
        }

        // Close modals
        closeButtons.forEach(button => {
            button.addEventListener('click', closeModals);
        });

        modalBackdrops.forEach(backdrop => {
            backdrop.addEventListener('click', closeModals);
        });

        // Copy link functionality
        const copyLinkButton = document.querySelector('.copy-link-button');
        if (copyLinkButton) {
            copyLinkButton.addEventListener('click', function() {
                const url = this.dataset.url;
                navigator.clipboard.writeText(url).then(() => {
                    // Show success message
                    const originalText = this.textContent;
                    this.textContent = 'Copied!';
                    setTimeout(() => {
                        this.textContent = originalText;
                    }, 2000);
                });
            });
        }

        // Copy share link
        const copyShareLink = document.getElementById('copy-share-link');
        if (copyShareLink) {
            copyShareLink.addEventListener('click', function() {
                const input = document.getElementById('share-link-input');
                input.select();
                navigator.clipboard.writeText(input.value).then(() => {
                    // Show success message
                    const originalText = this.textContent;
                    this.textContent = 'Copied!';
                    setTimeout(() => {
                        this.textContent = originalText;
                    }, 2000);
                });
            });
        }

        // Donation amount options
        const amountOptions = document.querySelectorAll('.amount-option');
        const donationAmount = document.getElementById('donation-amount');

        if (amountOptions.length > 0 && donationAmount) {
            amountOptions.forEach(option => {
                option.addEventListener('click', function() {
                    // Remove active class from all options
                    amountOptions.forEach(opt => opt.classList.remove('active'));

                    // Add active class to clicked option
                    this.classList.add('active');

                    // Set amount
                    if (this.classList.contains('custom')) {
                        donationAmount.value = '';
                        donationAmount.focus();
                    } else {
                        donationAmount.value = this.dataset.amount;
                    }
                });
            });
        }

        // Payment method selection
        const paymentMethods = document.querySelectorAll('.payment-method');

        if (paymentMethods.length > 0) {
            paymentMethods.forEach(method => {
                method.addEventListener('click', function() {
                    // Remove active class from all methods
                    paymentMethods.forEach(m => m.classList.remove('active'));

                    // Add active class to clicked method
                    this.classList.add('active');
                });
            });
        }

        // Donation form submission
        const donationForm = document.getElementById('donation-form');

        if (donationForm) {
            donationForm.addEventListener('submit', function(e) {
                e.preventDefault();

                // Get form data
                const amount = donationAmount.value;
                const message = document.getElementById('donation-message').value;
                const isAnonymous = document.getElementById('anonymous-donation').checked;

                // Get selected payment method
                let paymentMethod = '';
                document.querySelectorAll('.payment-method.active').forEach(method => {
                    paymentMethod = method.dataset.method;
                });

                // Validate form
                if (!amount) {
                    alert('Please enter a donation amount');
                    return;
                }

                if (!paymentMethod) {
                    alert('Please select a payment method');
                    return;
                }

                // In a real application, you would process the payment here
                // For demo purposes, we'll just show a success message

                // Show success message
                donationModal.querySelector('.modal-content').innerHTML = `
                    <h3>Thank You for Your Donation!</h3>
                    <p>Your donation of $${amount} has been processed successfully.</p>
                    <p>Your generosity makes a real difference.</p>
                    <button class="btn-primary close-modal">Close</button>
                `;

                // Add event listener to new close button
                donationModal.querySelector('.close-modal').addEventListener('click', closeModals);
            });
        }

        // Message form submission
        const messageForm = document.getElementById('message-form');

        if (messageForm) {
            messageForm.addEventListener('submit', function(e) {
                e.preventDefault();

                // Get form data
                const subject = document.getElementById('message-subject').value;
                const message = document.getElementById('message-content').value;
                const email = document.getElementById('message-email').value;

                // In a real application, you would send the message to the server here
                // For demo purposes, we'll just show a success message

                // Show success message
                messageModal.querySelector('.modal-content').innerHTML = `
                    <h3>Message Sent!</h3>
                    <p>Your message has been sent successfully.</p>
                    <p>You'll receive a reply at ${email} when the recipient responds.</p>
                    <button class="btn-primary close-modal">Close</button>
                `;

                // Add event listener to new close button
                messageModal.querySelector('.close-modal').addEventListener('click', closeModals);
            });
        }

        // Media tab functionality
        const mediaTabs = document.querySelectorAll('.media-tab');
        const mediaPanels = document.querySelectorAll('.media-panel');

        mediaTabs.forEach(tab => {
            tab.addEventListener('click', function() {
                // Remove active class from all tabs and panels
                mediaTabs.forEach(t => t.classList.remove('active'));
                mediaPanels.forEach(p => p.classList.remove('active'));

                // Add active class to clicked tab
                this.classList.add('active');

                // Show corresponding panel
                const tabType = this.dataset.tab;
                document.getElementById(tabType + '-panel').classList.add('active');
            });
        });

        // Handle image preview in post form
        const postImageInput = document.getElementById('post-image');
        const imagePreview = document.getElementById('image-preview');

        if (postImageInput && imagePreview) {
            postImageInput.addEventListener('change', function() {
                // Clear previous preview
                imagePreview.innerHTML = '';
                imagePreview.classList.remove('has-content');

                if (this.files && this.files[0]) {
                    const reader = new FileReader();

                    reader.onload = function(e) {
                        imagePreview.classList.add('has-content');

                        const img = document.createElement('img');
                        img.src = e.target.result;
                        img.alt = 'Image Preview';

                        const removeButton = document.createElement('button');
                        removeButton.type = 'button';
                        removeButton.className = 'btn-secondary';
                        removeButton.style.marginTop = '10px';
                        removeButton.innerHTML = '<i class="fas fa-times"></i> Remove Image';
                        removeButton.addEventListener('click', function() {
                            imagePreview.innerHTML = '';
                            imagePreview.classList.remove('has-content');
                            postImageInput.value = '';
                        });

                        imagePreview.appendChild(img);
                        imagePreview.appendChild(removeButton);
                    };

                    reader.readAsDataURL(this.files[0]);
                } else {
                    imagePreview.innerHTML = '<p>No image selected</p>';
                }
            });
        }

        // Handle video URL preview
        const videoUrlInput = document.getElementById('video-url');
        const videoPreview = document.getElementById('video-preview');

        if (videoUrlInput && videoPreview) {
            videoUrlInput.addEventListener('input', function() {
                const url = this.value.trim();
                videoPreview.innerHTML = '';
                videoPreview.classList.remove('has-content');

                if (url) {
                    // Simple YouTube and Vimeo URL detection
                    let embedUrl = '';

                    if (url.includes('youtube.com/watch?v=') || url.includes('youtu.be/')) {
                        const videoId = url.includes('youtu.be/')
                            ? url.split('youtu.be/')[1].split('?')[0]
                            : url.split('v=')[1].split('&')[0];
                        embedUrl = `https://www.youtube.com/embed/${videoId}`;
                    } else if (url.includes('vimeo.com/')) {
                        const videoId = url.split('vimeo.com/')[1].split('?')[0];
                        embedUrl = `https://player.vimeo.com/video/${videoId}`;
                    }

                    if (embedUrl) {
                        videoPreview.classList.add('has-content');
                        videoPreview.innerHTML = `
                            <iframe class="video-embed" src="${embedUrl}" frameborder="0" allowfullscreen></iframe>
                            <button type="button" class="btn-secondary" style="margin-top: 10px;" onclick="this.parentElement.innerHTML=''; this.parentElement.classList.remove('has-content'); document.getElementById('video-url').value='';">
                                <i class="fas fa-times"></i> Remove Video
                            </button>
                        `;
                    } else {
                        videoPreview.innerHTML = '<p>Enter a valid YouTube or Vimeo URL</p>';
                    }
                } else {
                    videoPreview.innerHTML = '<p>No video URL entered</p>';
                }
            });
        }

        // Handle link preview
        const linkUrlInput = document.getElementById('link-url');
        const linkTitleInput = document.getElementById('link-title');
        const linkPreview = document.getElementById('link-preview');

        function updateLinkPreview() {
            const url = linkUrlInput.value.trim();
            const title = linkTitleInput.value.trim();

            linkPreview.innerHTML = '';
            linkPreview.classList.remove('has-content');

            if (url) {
                linkPreview.classList.add('has-content');
                const displayTitle = title || url;
                linkPreview.innerHTML = `
                    <div class="link-preview">
                        <h5>${displayTitle}</h5>
                        <p>${url}</p>
                        <button type="button" class="btn-secondary" style="margin-top: 10px;" onclick="this.closest('.media-preview').innerHTML=''; this.closest('.media-preview').classList.remove('has-content'); document.getElementById('link-url').value=''; document.getElementById('link-title').value='';">
                            <i class="fas fa-times"></i> Remove Link
                        </button>
                    </div>
                `;
            } else {
                linkPreview.innerHTML = '<p>No link entered</p>';
            }
        }

        if (linkUrlInput && linkPreview) {
            linkUrlInput.addEventListener('input', updateLinkPreview);
            linkTitleInput.addEventListener('input', updateLinkPreview);
        }

        // New post form submission
        const newPostForm = document.getElementById('new-post-form');

        if (newPostForm) {
            newPostForm.addEventListener('submit', function(e) {
                e.preventDefault();

                // Get form data
                const content = tinymce.get('post-content') ? tinymce.get('post-content').getContent() : document.getElementById('post-content').value;
                const isPinned = document.getElementById('pin-post').checked;
                const allowComments = document.getElementById('allow-comments').checked;
                const imageFile = document.getElementById('post-image').files[0];
                const videoUrl = document.getElementById('video-url').value;
                const linkUrl = document.getElementById('link-url').value;
                const linkTitle = document.getElementById('link-title').value;

                // Validate content
                if (!content.trim()) {
                    alert('Please enter some content for your post.');
                    return;
                }

                // Prepare post data summary
                let mediaInfo = '';
                if (imageFile) {
                    mediaInfo += `<br><strong>Image:</strong> ${imageFile.name}`;
                }
                if (videoUrl) {
                    mediaInfo += `<br><strong>Video:</strong> ${videoUrl}`;
                }
                if (linkUrl) {
                    mediaInfo += `<br><strong>Link:</strong> ${linkTitle || linkUrl}`;
                }

                // Create FormData object for file upload
                const formData = new FormData();
                formData.append('content', content);
                formData.append('pin', isPinned ? '1' : '0');
                formData.append('allow_comments', allowComments ? '1' : '0');
                formData.append('video_url', videoUrl);
                formData.append('link_url', linkUrl);
                formData.append('link_title', linkTitle);

                if (imageFile) {
                    formData.append('image', imageFile);
                }

                // Show loading state
                const submitButton = newPostForm.querySelector('button[type="submit"]');
                const originalText = submitButton.innerHTML;
                submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Posting...';
                submitButton.disabled = true;

                // Send post to server
                fetch('create-post.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Show success message
                        newPostModal.querySelector('.post-modal-body').innerHTML = `
                            <div class="success-message">
                                <i class="fas fa-check-circle"></i>
                                <h3>Update Posted!</h3>
                                <p>Your update has been posted successfully!</p>
                                <div style="background: #f5f5f5; padding: 15px; border-radius: 8px; margin: 15px 0; text-align: left;">
                                    <strong>Content:</strong> ${content.substring(0, 100)}${content.length > 100 ? '...' : ''}
                                    ${mediaInfo}
                                    ${isPinned ? '<br><strong>Pinned:</strong> Yes' : ''}
                                    ${allowComments ? '<br><strong>Comments:</strong> Enabled' : '<br><strong>Comments:</strong> Disabled'}
                                </div>
                                <button class="btn-primary close-modal">Close</button>
                            </div>
                        `;

                        // Add event listener to new close button
                        newPostModal.querySelector('.close-modal').addEventListener('click', closeModals);

                        // Reload page after a short delay to show the new post
                        setTimeout(() => {
                            window.location.reload();
                        }, 2000);
                    } else {
                        // Show error message
                        alert('Error creating post: ' + (data.error || 'Unknown error'));

                        // Reset button
                        submitButton.innerHTML = originalText;
                        submitButton.disabled = false;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error creating post. Please try again.');

                    // Reset button
                    submitButton.innerHTML = originalText;
                    submitButton.disabled = false;
                });
            });
        }

        // Fulfill wishlist item
        const fulfillButtons = document.querySelectorAll('.fulfill-button');

        if (fulfillButtons.length > 0) {
            fulfillButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const itemId = this.dataset.itemId;
                    const itemTitle = this.closest('.wishlist-item').querySelector('h3').textContent;

                    // Show confirmation dialog
                    if (confirm(`Are you sure you want to help with "${itemTitle}"? You'll be redirected to a form to provide details.`)) {
                        // In a real application, you would redirect to a form or open a modal
                        // For demo purposes, we'll just show an alert
                        alert('Thank you for offering to help! In a real application, you would be redirected to a form to provide your contact details.');
                    }
                });
            });
        }

        // Toggle section visibility
        const toggleVisibilityButtons = document.querySelectorAll('.toggle-visibility-btn');

        if (toggleVisibilityButtons.length > 0) {
            toggleVisibilityButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const sectionId = this.dataset.sectionId;

                    if (confirm('Are you sure you want to hide this section? You can make it visible again from the edit profile page.')) {
                        // In a real application, you would send an AJAX request to toggle visibility
                        // For demo purposes, we'll just hide the section visually
                        const section = this.closest('.profile-section');
                        section.style.opacity = '0.5';
                        section.style.pointerEvents = 'none';

                        // Show a message
                        const sectionHeader = section.querySelector('.section-header h2');
                        sectionHeader.innerHTML += ' <span style="color: #999; font-size: 0.8em;">(Hidden)</span>';

                        // Change button icon and tooltip
                        this.innerHTML = '<i class="fas fa-eye"></i>';
                        this.title = 'Show this section';

                        // Update the click handler to show instead of hide
                        this.onclick = function() {
                            section.style.opacity = '1';
                            section.style.pointerEvents = 'auto';
                            sectionHeader.innerHTML = sectionHeader.innerHTML.replace(' <span style="color: #999; font-size: 0.8em;">(Hidden)</span>', '');
                            this.innerHTML = '<i class="fas fa-eye-slash"></i>';
                            this.title = 'Hide this section';
                            this.onclick = null; // Reset to original handler
                        };
                    }
                });
            });
        }
    });
</script>

<?php
// Include footer
include_once 'templates/components/footer.php';
?>
