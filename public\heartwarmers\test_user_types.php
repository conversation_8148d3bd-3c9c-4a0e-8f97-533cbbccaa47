<?php
/**
 * Test script for user type registration system
 * This script tests the three different user types and their registration
 */

// Include necessary files
require_once 'php/includes/db.php';
require_once 'php/includes/functions.php';
require_once 'php/includes/user-functions.php';

// Set page variables
$pageTitle = 'Test User Types - Heartwarmers';
$pageDescription = 'Test the three user type registration system';
$currentPage = 'test';
$pageStyles = ['css/auth.css'];

// Include header
include_once 'templates/components/header.php';

// Test data for each user type
$testUsers = [
    [
        'username' => 'TestHelpSeeker',
        'email' => '<EMAIL>',
        'password' => 'testpass123',
        'location' => 'Test City, NC',
        'user_type' => 'help_seeker'
    ],
    [
        'username' => 'TestOrganization',
        'email' => '<EMAIL>',
        'password' => 'testpass123',
        'location' => 'Test City, NC',
        'user_type' => 'business_organization'
    ],
    [
        'username' => 'TestVolunteer',
        'email' => '<EMAIL>',
        'password' => 'testpass123',
        'location' => 'Test City, NC',
        'user_type' => 'volunteer_mutual_aid'
    ]
];

$results = [];
$error = '';

// Process test if requested
if (isset($_POST['run_test'])) {
    foreach ($testUsers as $testUser) {
        $result = register_user(
            $testUser['username'],
            $testUser['email'],
            $testUser['password'],
            $testUser['location'],
            $testUser['user_type']
        );
        
        $results[] = [
            'user_type' => $testUser['user_type'],
            'username' => $testUser['username'],
            'result' => $result
        ];
    }
}
?>

<div class="auth-page">
    <div class="container">
        <div class="auth-card">
            <div class="auth-header">
                <h1>Test User Type Registration</h1>
                <p>This page tests the three user type registration system</p>
            </div>
            
            <?php if (!empty($error)): ?>
                <div class="alert alert-error">
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>
            
            <div class="test-info">
                <h3>User Types Being Tested:</h3>
                <ul>
                    <li><strong>Help Seeker:</strong> Person seeking assistance</li>
                    <li><strong>Business/Organization:</strong> Organizations offering services</li>
                    <li><strong>Volunteer/Mutual Aid:</strong> Volunteers and mutual aid providers</li>
                </ul>
            </div>
            
            <?php if (empty($results)): ?>
                <form method="post" action="test_user_types.php" class="auth-form">
                    <div class="form-actions">
                        <button type="submit" name="run_test" class="btn-primary">Run Registration Test</button>
                    </div>
                </form>
            <?php else: ?>
                <div class="test-results">
                    <h3>Test Results:</h3>
                    <?php foreach ($results as $result): ?>
                        <div class="test-result">
                            <h4><?php echo ucfirst(str_replace('_', ' ', $result['user_type'])); ?> - <?php echo htmlspecialchars($result['username']); ?></h4>
                            <?php if (isset($result['result']['error'])): ?>
                                <div class="alert alert-error">
                                    Error: <?php echo htmlspecialchars($result['result']['error']); ?>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-success">
                                    ✓ User registered successfully! 
                                    <br>User ID: <?php echo $result['result']['id']; ?>
                                    <br>User Type: <?php echo $result['result']['user_type'] ?? 'Not set'; ?>
                                    <br><a href="user-profile.php?id=<?php echo $result['result']['id']; ?>" target="_blank">View Profile</a>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <div class="form-actions">
                    <a href="test_user_types.php" class="btn-secondary">Run Test Again</a>
                    <a href="register.php" class="btn-primary">Test Registration Form</a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
.test-info, .test-results {
    margin: 1.5rem 0;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 8px;
}

.test-result {
    margin: 1rem 0;
    padding: 1rem;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    background-color: white;
}

.test-result h4 {
    margin: 0 0 0.5rem 0;
    color: #495057;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 4px;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-secondary:hover {
    background-color: #5a6268;
}
</style>

<?php
// Include footer
include_once 'templates/components/footer.php';
?>
