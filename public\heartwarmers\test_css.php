<?php
/**
 * Test page to verify CSS and JS loading
 */

// Set page variables
$pageTitle = 'CSS Test - Heartwarmers';
$pageDescription = 'Test page to verify CSS and JavaScript loading';
$currentPage = 'test';
$pageStyles = ['css/test.css', 'css/auth.css', 'css/profile.css'];
$pageScripts = ['js/blog-carousel.js'];

// Include header
include_once 'templates/components/header.php';
?>

<div class="auth-page">
    <div class="container">
        <div class="auth-card">
            <div class="auth-header">
                <h1>CSS and JavaScript Test</h1>
                <p>This page tests if CSS and JavaScript files are loading correctly</p>
            </div>
            
            <div class="test-section">
                <h3>Path Debugging</h3>
                <p><strong>Debug Information:</strong></p>
                <ul>
                    <li>REQUEST_URI: <?php echo $_SERVER['REQUEST_URI']; ?></li>
                    <li>SCRIPT_NAME: <?php echo $_SERVER['SCRIPT_NAME']; ?></li>
                    <li>DOCUMENT_ROOT: <?php echo $_SERVER['DOCUMENT_ROOT']; ?></li>
                    <li>SCRIPT_FILENAME: <?php echo $_SERVER['SCRIPT_FILENAME']; ?></li>
                </ul>
            </div>

            <div class="test-section">
                <h3>CSS Test</h3>
                <p>If you can see this page styled properly with:</p>
                <ul>
                    <li>Blue primary colors</li>
                    <li>Rounded corners on this card</li>
                    <li>Proper typography</li>
                    <li>Styled buttons below</li>
                </ul>
                <p>Then the CSS is loading correctly.</p>
                
                <div class="form-actions">
                    <button class="btn-primary">Primary Button</button>
                    <button class="btn-secondary">Secondary Button</button>
                </div>
            </div>
            
            <div class="test-section">
                <h3>User Type Badge Test</h3>
                <p>Testing the user type badges:</p>
                <div style="display: flex; gap: 1rem; flex-wrap: wrap; justify-content: center; margin: 1rem 0;">
                    <span class="user-type-badge" style="background-color: #ef4444; color: white; padding: 4px 12px; border-radius: 20px; font-size: 0.875rem; font-weight: 500; display: flex; align-items: center; gap: 6px;">
                        <i class="fas fa-heart"></i>
                        Seeking Help
                    </span>
                    <span class="user-type-badge" style="background-color: #3b82f6; color: white; padding: 4px 12px; border-radius: 20px; font-size: 0.875rem; font-weight: 500; display: flex; align-items: center; gap: 6px;">
                        <i class="fas fa-building"></i>
                        Organization
                    </span>
                    <span class="user-type-badge" style="background-color: #10b981; color: white; padding: 4px 12px; border-radius: 20px; font-size: 0.875rem; font-weight: 500; display: flex; align-items: center; gap: 6px;">
                        <i class="fas fa-hands-helping"></i>
                        Volunteer
                    </span>
                </div>
            </div>
            
            <div class="test-section">
                <h3>Font Awesome Icons Test</h3>
                <p>Testing Font Awesome icons:</p>
                <div style="font-size: 2rem; text-align: center; margin: 1rem 0;">
                    <i class="fas fa-heart" style="color: #ef4444; margin: 0 0.5rem;"></i>
                    <i class="fas fa-building" style="color: #3b82f6; margin: 0 0.5rem;"></i>
                    <i class="fas fa-hands-helping" style="color: #10b981; margin: 0 0.5rem;"></i>
                    <i class="fas fa-user" style="color: #6b7280; margin: 0 0.5rem;"></i>
                    <i class="fas fa-map-marker-alt" style="color: #fbbf24; margin: 0 0.5rem;"></i>
                </div>
            </div>
            
            <div class="test-section">
                <h3>Navigation Test</h3>
                <p>Test the registration system:</p>
                <div class="form-actions">
                    <a href="register.php" class="btn-primary">Test Registration Form</a>
                    <a href="test_user_types.php" class="btn-secondary">Test User Types</a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.test-section {
    margin: 2rem 0;
    padding: 1.5rem;
    background-color: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid var(--primary-color);
}

.test-section h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.test-section ul {
    margin: 1rem 0;
    padding-left: 2rem;
}

.test-section li {
    margin: 0.5rem 0;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 4px;
    text-decoration: none;
    display: inline-block;
    text-align: center;
    cursor: pointer;
    transition: background-color 0.2s;
}

.btn-secondary:hover {
    background-color: #5a6268;
    text-decoration: none;
    color: white;
}
</style>

<script>
// Test JavaScript
console.log('CSS Test page JavaScript loaded successfully');

// Test if jQuery is available (if it's included)
if (typeof $ !== 'undefined') {
    console.log('jQuery is available');
} else {
    console.log('jQuery is not available');
}

// Add a simple interaction test
document.addEventListener('DOMContentLoaded', function() {
    const buttons = document.querySelectorAll('.btn-primary, .btn-secondary');
    buttons.forEach(button => {
        if (button.tagName === 'BUTTON') {
            button.addEventListener('click', function() {
                alert('Button clicked! CSS and JavaScript are working.');
            });
        }
    });
});
</script>

<?php
// Include footer
include_once 'templates/components/footer.php';
?>
