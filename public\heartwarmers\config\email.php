<?php
/**
 * Email Configuration
 * 
 * This file contains email settings for the Heartwarmers application.
 * You can disable email notifications here if you don't have SMTP configured.
 */

return [
    // Enable or disable email notifications globally
    'enabled' => false, // Set to true when you have SMTP configured
    
    // Development mode - logs emails instead of sending them
    'development_mode' => true, // Set to false in production
    
    // SMTP Configuration (when enabled)
    'smtp' => [
        'host' => 'localhost',
        'port' => 587,
        'username' => '',
        'password' => '',
        'encryption' => 'tls', // tls, ssl, or null
    ],
    
    // Default sender information
    'from' => [
        'email' => '<EMAIL>',
        'name' => 'Heartwarmers Project'
    ],
    
    // Email templates
    'templates' => [
        'testimonial_submitted' => [
            'subject' => 'New Testimonial Submitted for Your Profile',
            'template' => 'emails/testimonial_submitted.php'
        ],
        'testimonial_approved' => [
            'subject' => 'Your Testimonial Has Been Approved',
            'template' => 'emails/testimonial_approved.php'
        ]
    ],
    
    // Notification settings
    'notifications' => [
        'testimonial_submitted' => true,
        'testimonial_approved' => true,
        'profile_updated' => false,
        'new_user_registration' => false
    ]
];
?>
