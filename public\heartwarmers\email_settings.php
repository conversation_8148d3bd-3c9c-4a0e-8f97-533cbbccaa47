<?php
/**
 * Email Settings Configuration Page
 * Allows you to enable/disable email notifications and configure SMTP settings
 */

// Include necessary files
require_once 'php/includes/functions.php';

// Set page variables
$pageTitle = 'Email Settings - Heartwarmers';
$pageDescription = 'Configure email notifications and SMTP settings';
$currentPage = 'admin';
$pageStyles = ['css/auth.css'];

$message = '';
$messageType = '';

// Load current email configuration
$configFile = 'config/email.php';
$emailConfig = [];
if (file_exists($configFile)) {
    $emailConfig = include $configFile;
}

// Default values
$enabled = $emailConfig['enabled'] ?? false;
$developmentMode = $emailConfig['development_mode'] ?? true;
$smtpHost = $emailConfig['smtp']['host'] ?? 'localhost';
$smtpPort = $emailConfig['smtp']['port'] ?? 587;
$fromEmail = $emailConfig['from']['email'] ?? '<EMAIL>';
$fromName = $emailConfig['from']['name'] ?? 'Heartwarmers Project';

// Process form submission
if (isset($_POST['save_settings'])) {
    $enabled = isset($_POST['enabled']);
    $developmentMode = isset($_POST['development_mode']);
    $smtpHost = sanitize_input($_POST['smtp_host'] ?? 'localhost');
    $smtpPort = (int)($_POST['smtp_port'] ?? 587);
    $fromEmail = sanitize_input($_POST['from_email'] ?? '<EMAIL>');
    $fromName = sanitize_input($_POST['from_name'] ?? 'Heartwarmers Project');
    
    // Create new configuration array
    $newConfig = [
        'enabled' => $enabled,
        'development_mode' => $developmentMode,
        'smtp' => [
            'host' => $smtpHost,
            'port' => $smtpPort,
            'username' => '',
            'password' => '',
            'encryption' => 'tls',
        ],
        'from' => [
            'email' => $fromEmail,
            'name' => $fromName
        ],
        'templates' => [
            'testimonial_submitted' => [
                'subject' => 'New Testimonial Submitted for Your Profile',
                'template' => 'emails/testimonial_submitted.php'
            ],
            'testimonial_approved' => [
                'subject' => 'Your Testimonial Has Been Approved',
                'template' => 'emails/testimonial_approved.php'
            ]
        ],
        'notifications' => [
            'testimonial_submitted' => true,
            'testimonial_approved' => true,
            'profile_updated' => false,
            'new_user_registration' => false
        ]
    ];
    
    // Write configuration to file
    $configContent = "<?php\n/**\n * Email Configuration\n * Generated by email settings page\n */\n\nreturn " . var_export($newConfig, true) . ";\n?>";
    
    if (file_put_contents($configFile, $configContent)) {
        $message = 'Email settings saved successfully!';
        $messageType = 'success';
    } else {
        $message = 'Failed to save email settings. Check file permissions.';
        $messageType = 'error';
    }
}

// Include header
include_once 'templates/components/header.php';
?>

<div class="auth-page">
    <div class="container">
        <div class="auth-card">
            <div class="auth-header">
                <h1>Email Settings</h1>
                <p>Configure email notifications and SMTP settings for the Heartwarmers website</p>
            </div>
            
            <?php if (!empty($message)): ?>
                <div class="alert alert-<?php echo $messageType; ?>">
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>
            
            <form method="post" action="email_settings.php" class="auth-form">
                <div class="form-section">
                    <h3>General Settings</h3>
                    
                    <div class="form-group">
                        <label>
                            <input type="checkbox" name="enabled" <?php echo $enabled ? 'checked' : ''; ?>>
                            Enable Email Notifications
                        </label>
                        <p class="form-help">Turn this on when you have SMTP configured and want to send emails</p>
                    </div>
                    
                    <div class="form-group">
                        <label>
                            <input type="checkbox" name="development_mode" <?php echo $developmentMode ? 'checked' : ''; ?>>
                            Development Mode
                        </label>
                        <p class="form-help">In development mode, emails are logged instead of sent</p>
                    </div>
                </div>
                
                <div class="form-section">
                    <h3>SMTP Settings</h3>
                    
                    <div class="form-group">
                        <label for="smtp_host">SMTP Host</label>
                        <input type="text" id="smtp_host" name="smtp_host" value="<?php echo htmlspecialchars($smtpHost); ?>">
                        <p class="form-help">Your email server hostname (e.g., smtp.gmail.com)</p>
                    </div>
                    
                    <div class="form-group">
                        <label for="smtp_port">SMTP Port</label>
                        <input type="number" id="smtp_port" name="smtp_port" value="<?php echo $smtpPort; ?>">
                        <p class="form-help">Usually 587 for TLS or 465 for SSL</p>
                    </div>
                </div>
                
                <div class="form-section">
                    <h3>Sender Information</h3>
                    
                    <div class="form-group">
                        <label for="from_email">From Email</label>
                        <input type="email" id="from_email" name="from_email" value="<?php echo htmlspecialchars($fromEmail); ?>">
                        <p class="form-help">Email address that notifications will be sent from</p>
                    </div>
                    
                    <div class="form-group">
                        <label for="from_name">From Name</label>
                        <input type="text" id="from_name" name="from_name" value="<?php echo htmlspecialchars($fromName); ?>">
                        <p class="form-help">Display name for outgoing emails</p>
                    </div>
                </div>
                
                <div class="current-status">
                    <h3>Current Status</h3>
                    <ul>
                        <li><strong>Email Enabled:</strong> <?php echo $enabled ? '✅ Yes' : '❌ No'; ?></li>
                        <li><strong>Development Mode:</strong> <?php echo $developmentMode ? '✅ Yes (emails logged)' : '❌ No (emails sent)'; ?></li>
                        <li><strong>Mail Function:</strong> <?php echo function_exists('mail') ? '✅ Available' : '❌ Not available'; ?></li>
                        <li><strong>Environment:</strong> <?php echo (strpos($_SERVER['HTTP_HOST'] ?? '', 'localhost') !== false) ? '🔧 Development' : '🌐 Production'; ?></li>
                    </ul>
                </div>
                
                <div class="form-actions">
                    <button type="submit" name="save_settings" class="btn-primary">Save Settings</button>
                    <a href="submit-testimonial.php" class="btn-secondary">Test Testimonial</a>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.form-section {
    margin: 2rem 0;
    padding: 1.5rem;
    background-color: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid var(--primary-color);
}

.form-section h3 {
    margin-top: 0;
    color: var(--primary-color);
}

.current-status {
    margin: 2rem 0;
    padding: 1rem;
    background-color: #e9ecef;
    border-radius: 8px;
}

.current-status ul {
    margin: 1rem 0;
    padding-left: 2rem;
}

.current-status li {
    margin: 0.5rem 0;
}

.alert {
    padding: 1rem;
    border-radius: 4px;
    margin: 1rem 0;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 4px;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-secondary:hover {
    background-color: #5a6268;
    text-decoration: none;
    color: white;
}
</style>

<?php
// Include footer
include_once 'templates/components/footer.php';
?>
