<?php
/**
 * Path Test Script
 * 
 * This script helps verify the correct path to the config file
 */

echo "<h1>Path Test Script</h1>";
echo "<style>body { font-family: Arial, sans-serif; } .success { color: green; } .error { color: red; } .info { color: blue; }</style>";

echo "<h2>Current Directory Information</h2>";
echo "<p class='info'><strong>Current working directory:</strong> " . getcwd() . "</p>";
echo "<p class='info'><strong>Script directory:</strong> " . __DIR__ . "</p>";
echo "<p class='info'><strong>Script file:</strong> " . __FILE__ . "</p>";

echo "<h2>Testing Different Config Paths</h2>";

// Test different possible paths
$possible_paths = [
    'orders/config.php',
    './orders/config.php',
    __DIR__ . '/orders/config.php',
    'config.php',
    './config.php',
    __DIR__ . '/config.php'
];

foreach ($possible_paths as $path) {
    $full_path = $path;
    if (!str_contains($path, __DIR__) && !str_starts_with($path, '/') && !str_contains($path, ':')) {
        $full_path = __DIR__ . '/' . $path;
    }
    
    echo "<p>";
    echo "<strong>Testing path:</strong> " . htmlspecialchars($path) . "<br>";
    echo "<strong>Full path:</strong> " . htmlspecialchars($full_path) . "<br>";
    
    if (file_exists($full_path)) {
        echo "<span class='success'>✓ File exists</span><br>";
        
        // Try to include it
        try {
            $backup_vars = get_defined_vars();
            include $full_path;
            
            if (defined('ADMIN_EMAIL')) {
                echo "<span class='success'>✓ Config loaded successfully</span><br>";
                echo "<span class='info'>Admin Email: " . ADMIN_EMAIL . "</span>";
            } else {
                echo "<span class='error'>✗ Config loaded but ADMIN_EMAIL not defined</span>";
            }
        } catch (Exception $e) {
            echo "<span class='error'>✗ Error loading config: " . $e->getMessage() . "</span>";
        }
    } else {
        echo "<span class='error'>✗ File does not exist</span>";
    }
    echo "</p><hr>";
}

echo "<h2>Directory Listing</h2>";
echo "<h3>Current Directory Contents:</h3>";
$current_dir = __DIR__;
if (is_dir($current_dir)) {
    $files = scandir($current_dir);
    echo "<ul>";
    foreach ($files as $file) {
        if ($file !== '.' && $file !== '..') {
            $file_path = $current_dir . '/' . $file;
            $type = is_dir($file_path) ? 'DIR' : 'FILE';
            echo "<li><strong>[$type]</strong> " . htmlspecialchars($file) . "</li>";
        }
    }
    echo "</ul>";
}

echo "<h3>Orders Subdirectory Contents:</h3>";
$orders_dir = $current_dir . '/orders';
if (is_dir($orders_dir)) {
    $files = scandir($orders_dir);
    echo "<ul>";
    foreach ($files as $file) {
        if ($file !== '.' && $file !== '..') {
            $file_path = $orders_dir . '/' . $file;
            $type = is_dir($file_path) ? 'DIR' : 'FILE';
            echo "<li><strong>[$type]</strong> " . htmlspecialchars($file) . "</li>";
        }
    }
    echo "</ul>";
} else {
    echo "<p class='error'>Orders directory does not exist</p>";
}

echo "<h2>Recommended Solution</h2>";
if (file_exists(__DIR__ . '/orders/config.php')) {
    echo "<p class='success'>✓ Use: require_once 'orders/config.php';</p>";
} elseif (file_exists(__DIR__ . '/config.php')) {
    echo "<p class='success'>✓ Use: require_once 'config.php';</p>";
} else {
    echo "<p class='error'>✗ Config file not found in expected locations</p>";
    echo "<p class='info'>You may need to check the file structure or move the config file</p>";
}
?>
