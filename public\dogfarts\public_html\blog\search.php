<?php
/**
 * Blog Search Results
 * 
 * This script searches for blog posts based on a query string
 * and displays the results.
 */

// Include necessary files
require_once '../config/config.php';
require_once '../config/db.php';
require_once '../lib/functions.php';
include_once '../includes/header.php';
include_once '../includes/nav.php';

// Get search query from URL
$search_query = isset($_GET['q']) ? trim($_GET['q']) : '';

// Initialize variables
$posts = [];
$error = '';

// Validate search query
if (empty($search_query)) {
    $error = 'Please enter a search term';
} else {
    try {
        // Get database connection
        $db = getDbConnection();
        
        // Prepare search query with prepared statement
        $stmt = $db->prepare("
            SELECT 
                p.id, 
                p.title, 
                p.content, 
                p.date_published, 
                p.author_id,
                a.name as author_name
            FROM 
                posts p
            LEFT JOIN 
                authors a ON p.author_id = a.id
            WHERE 
                (p.title LIKE :search_term OR p.content LIKE :search_term)
                AND p.status = 'published'
            ORDER BY 
                p.date_published DESC
        ");
        
        // Add wildcards to search term
        $search_term = '%' . $search_query . '%';
        $stmt->bindParam(':search_term', $search_term, PDO::PARAM_STR);
        $stmt->execute();
        $posts = $stmt->fetchAll();
        
        // For each post, fetch its categories
        foreach ($posts as $key => $post) {
            $stmt = $db->prepare("
                SELECT 
                    c.id, 
                    c.name, 
                    c.slug
                FROM 
                    categories c
                JOIN 
                    post_categories pc ON c.id = pc.category_id
                WHERE 
                    pc.post_id = :post_id
                ORDER BY 
                    c.name ASC
            ");
            
            $stmt->bindParam(':post_id', $post['id'], PDO::PARAM_INT);
            $stmt->execute();
            $posts[$key]['categories'] = $stmt->fetchAll();
        }
    } catch (PDOException $e) {
        // Log the error (in a production environment)
        error_log('Database query error: ' . $e->getMessage());
        
        // Set error message
        $error = 'Sorry, we could not process your search request.';
    }
}
?>

<main class="blog-search">
    <h1>Search Results: <?php echo htmlspecialchars($search_query); ?></h1>
    
    <div class="search-form">
        <form action="search.php" method="get">
            <div class="search-input">
                <input type="text" name="q" value="<?php echo htmlspecialchars($search_query); ?>" placeholder="Search blog posts...">
                <button type="submit">Search</button>
            </div>
        </form>
    </div>
    
    <?php if ($error): ?>
        <div class="error-message">
            <?php echo htmlspecialchars($error); ?>
        </div>
    <?php elseif (empty($posts)): ?>
        <div class="no-results">
            <p>No posts found matching your search term.</p>
            <p>Try using different keywords or check your spelling.</p>
        </div>
    <?php else: ?>
        <div class="search-results-count">
            <p>Found <?php echo count($posts); ?> result<?php echo count($posts) != 1 ? 's' : ''; ?> for "<?php echo htmlspecialchars($search_query); ?>"</p>
        </div>
        
        <div class="posts-container">
            <?php foreach ($posts as $post): ?>
                <article class="post">
                    <h2 class="post-title">
                        <a href="post.php?id=<?php echo $post['id']; ?>">
                            <?php echo htmlspecialchars($post['title']); ?>
                        </a>
                    </h2>
                    
                    <div class="post-meta">
                        <span class="post-date">
                            <?php echo formatDate($post['date_published']); ?>
                        </span>
                        
                        <?php if (!empty($post['author_name'])): ?>
                            <span class="post-author">
                                by <?php echo htmlspecialchars($post['author_name']); ?>
                            </span>
                        <?php endif; ?>
                        
                        <?php if (!empty($post['categories'])): ?>
                            <span class="post-categories">
                                in 
                                <?php 
                                $categoryLinks = [];
                                foreach ($post['categories'] as $category) {
                                    $categoryLinks[] = '<a href="category.php?slug=' . 
                                        htmlspecialchars($category['slug']) . '">' . 
                                        htmlspecialchars($category['name']) . '</a>';
                                }
                                echo implode(', ', $categoryLinks);
                                ?>
                            </span>
                        <?php endif; ?>
                    </div>
                    
                    <div class="post-excerpt">
                        <?php 
                        // Create an excerpt from the content
                        echo createExcerpt($post['content']);
                        ?>
                        <a href="post.php?id=<?php echo $post['id']; ?>" class="read-more">
                            Read More
                        </a>
                    </div>
                </article>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>
    
    <div class="search-navigation">
        <a href="index.php" class="back-to-blog">← Back to Blog</a>
    </div>
</main>

<?php
include 'sidebar.php';
include '../includes/footer.php';
?>
