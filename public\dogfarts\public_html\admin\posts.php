<?php
// In a real implementation, this would include authentication check
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: index.php');
    exit;
}

// Placeholder posts data
$posts = [
    [
        'id' => 1,
        'title' => 'New Magical Creatures Course',
        'date' => '2023-05-10',
        'status' => 'published'
    ],
    [
        'id' => 2,
        'title' => 'Quidditch Season Results',
        'date' => '2023-05-05',
        'status' => 'published'
    ],
    [
        'id' => 3,
        'title' => 'Upcoming Summer Events',
        'date' => '2023-05-15',
        'status' => 'draft'
    ]
];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Posts - Dogfarts School Admin</title>
    <link rel="stylesheet" href="/assets/css/main.css">
</head>
<body class="admin">
    <header class="admin-header">
        <h1>Dogfarts School Admin</h1>
        <nav>
            <ul>
                <li><a href="posts.php" class="active">Manage Posts</a></li>
                <li><a href="#">Manage Pages</a></li>
                <li><a href="#">Settings</a></li>
                <li><a href="?logout=1">Logout</a></li>
            </ul>
        </nav>
    </header>
    
    <main class="admin-content">
        <h2>Manage Blog Posts</h2>
        
        <div class="admin-actions">
            <a href="#" class="button">Add New Post</a>
        </div>
        
        <table class="posts-table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Title</th>
                    <th>Date</th>
                    <th>Status</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($posts as $post): ?>
                <tr>
                    <td><?php echo $post['id']; ?></td>
                    <td><?php echo htmlspecialchars($post['title']); ?></td>
                    <td><?php echo htmlspecialchars($post['date']); ?></td>
                    <td><?php echo htmlspecialchars($post['status']); ?></td>
                    <td>
                        <a href="#" class="action edit">Edit</a>
                        <a href="#" class="action delete">Delete</a>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </main>
    
    <footer class="admin-footer">
        <p>&copy; <?php echo date('Y'); ?> Dogfarts School of Witchcraft and Wizardry</p>
    </footer>
</body>
</html>
