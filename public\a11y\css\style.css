/* Base Styles */
:root {
    --primary-color: #0056b3;
    --secondary-color: #6c757d;
    --text-color: #212529;
    --bg-color: #ffffff;
    --link-color: #0056b3;
    --link-hover: #003d7a;
    --font-main: 'Se<PERSON>e UI', <PERSON><PERSON>, 'Helvetica Neue', sans-serif;
    --font-size: 16px;
    --line-height: 1.5;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: var(--font-main);
    font-size: var(--font-size);
    line-height: var(--line-height);
    color: var(--text-color);
    background-color: var(--bg-color);
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Skip Link */
.skip-link {
    position: absolute;
    top: -40px;
    left: 0;
    background: #000;
    color: white;
    padding: 8px;
    z-index: 100;
    transition: top 0.3s;
}

.skip-link:focus {
    top: 0;
}

/* Header */
.site-header {
    background-color: var(--primary-color);
    color: white;
    padding: 1rem 0;
}

.site-title {
    font-size: 1.75rem;
    margin-bottom: 0.25rem;
}

.site-tagline {
    font-size: 1rem;
    opacity: 0.9;
}

/* Navigation */
#menu-toggle {
    display: none;
    background: none;
    border: none;
    color: white;
    font-size: 1rem;
    cursor: pointer;
    padding: 0.5rem;
}

#main-menu {
    display: flex;
    list-style: none;
    margin-top: 1rem;
}

#main-menu li {
    margin-right: 1rem;
}

#main-menu a {
    color: white;
    text-decoration: none;
    padding: 0.5rem;
}

#main-menu a:hover,
#main-menu a:focus {
    text-decoration: underline;
}

#main-menu a[aria-current="page"] {
    font-weight: bold;
    text-decoration: underline;
}

/* Hero Section */
.hero {
    background-color: #f8f9fa;
    padding: 3rem 0;
    text-align: center;
}

.hero h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.hero p {
    font-size: 1.25rem;
    max-width: 800px;
    margin: 0 auto 2rem;
}

/* Buttons */
.button {
    display: inline-block;
    background-color: var(--primary-color);
    color: white;
    padding: 0.75rem 1.5rem;
    text-decoration: none;
    border-radius: 4px;
    font-weight: bold;
    margin: 0.5rem;
}

.button:hover,
.button:focus {
    background-color: var(--link-hover);
    text-decoration: none;
}

.button.secondary {
    background-color: var(--secondary-color);
}

.button.secondary:hover,
.button.secondary:focus {
    background-color: #5a6268;
}

/* Resource Grid */
.featured-resources {
    padding: 3rem 0;
}

.resource-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.resource-card {
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 1.5rem;
    transition: box-shadow 0.3s;
}

.resource-card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.resource-card h3 {
    margin-bottom: 1rem;
}

.resource-card h3 a {
    color: var(--text-color);
    text-decoration: none;
}

.resource-card h3 a:hover,
.resource-card h3 a:focus {
    color: var(--primary-color);
    text-decoration: underline;
}

/* Footer */
.site-footer {
    background-color: #343a40;
    color: white;
    padding: 2rem 0;
    margin-top: 3rem;
}

.footer-contact a {
    color: white;
}

.footer-accessibility {
    margin-top: 1rem;
}

.footer-accessibility button {
    background: none;
    border: 1px solid white;
    color: white;
    padding: 0.5rem 1rem;
    margin-right: 0.5rem;
    cursor: pointer;
}

.footer-accessibility button:hover,
.footer-accessibility button:focus {
    background-color: rgba(255, 255, 255, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
    #menu-toggle {
        display: block;
    }
    
    #main-menu {
        display: none;
        flex-direction: column;
    }
    
    #main-menu[aria-expanded="true"] {
        display: flex;
    }
    
    .hero h2 {
        font-size: 2rem;
    }
    
    .hero p {
        font-size: 1.1rem;
    }
}