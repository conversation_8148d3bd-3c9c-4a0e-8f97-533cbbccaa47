<?php
/**
 * Blog Sidebar
 * 
 * This file contains the sidebar for the blog section.
 */

// Get database connection if not already available
if (!isset($db) || !($db instanceof PDO)) {
    require_once '../config/db.php';
    $db = getDbConnection();
}

// Initialize variables
$recent_posts = [];
$error = '';

try {
    // Fetch recent posts
    $stmt = $db->prepare("
        SELECT 
            id, 
            title, 
            date_published
        FROM 
            posts
        WHERE 
            status = 'published'
        ORDER BY 
            date_published DESC
        LIMIT 5
    ");
    
    $stmt->execute();
    $recent_posts = $stmt->fetchAll();
} catch (PDOException $e) {
    // Log the error (in a production environment)
    error_log('Database query error: ' . $e->getMessage());
    
    // Set error message
    $error = 'Could not retrieve recent posts.';
}
?>

<aside class="sidebar">
    <!-- Categories Widget -->
    <?php include 'sidebar-categories.php'; ?>
    
    <!-- Recent Posts Widget -->
    <div class="widget recent-posts">
        <h3>Recent Posts</h3>
        
        <?php if ($error): ?>
            <p class="error"><?php echo htmlspecialchars($error); ?></p>
        <?php elseif (empty($recent_posts)): ?>
            <p>No recent posts found.</p>
        <?php else: ?>
            <ul>
                <?php foreach ($recent_posts as $post): ?>
                    <li>
                        <a href="post.php?id=<?php echo $post['id']; ?>">
                            <?php echo htmlspecialchars($post['title']); ?>
                        </a>
                        <span class="post-date">
                            <?php echo formatDate($post['date_published'], 'M j, Y'); ?>
                        </span>
                    </li>
                <?php endforeach; ?>
            </ul>
        <?php endif; ?>
    </div>
    
    <!-- Search Widget -->
    <div class="widget blog-search">
        <h3>Search</h3>
        <form action="search.php" method="get">
            <div class="search-input">
                <input type="text" name="q" placeholder="Search blog posts...">
                <button type="submit">Search</button>
            </div>
        </form>
    </div>
    
    <!-- School Info Widget -->
    <div class="widget school-info">
        <h3>About Dogfarts School</h3>
        <p>Dogfarts School of Witchcraft and Wizardry is the premier magical education institution for aspiring witches and wizards.</p>
        <p><a href="../index.php">Visit School Website</a></p>
    </div>
</aside>
