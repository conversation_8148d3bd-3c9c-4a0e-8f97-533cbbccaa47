<?php
/**
 * Database Setup Script
 * 
 * This script sets up the database schema and sample data for the blog.
 * IMPORTANT: This should only be run once to initialize the database.
 * For security, this file should be removed or protected after use.
 */

// Basic security check - require a confirmation parameter
if (!isset($_GET['confirm']) || $_GET['confirm'] !== 'yes') {
    die('For security, this script requires a confirmation parameter. Add ?confirm=yes to the URL to proceed.');
}

// Include database configuration
require_once '../config/db.php';

try {
    // Get database connection
    $pdo = new PDO('mysql:host=' . DB_HOST, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Create database if it doesn't exist
    $pdo->exec("CREATE DATABASE IF NOT EXISTS " . DB_NAME . " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<p>Database created or already exists.</p>";
    
    // Select the database
    $pdo->exec("USE " . DB_NAME);
    
    // Read and execute the SQL schema file
    $sql = file_get_contents('../config/blog_schema.sql');
    $pdo->exec($sql);
    
    echo "<p>Database schema and sample data have been set up successfully!</p>";
    echo "<p>You can now <a href='../blog/index.php'>view the blog</a>.</p>";
    
} catch (PDOException $e) {
    die("Database setup failed: " . $e->getMessage());
}
?>
