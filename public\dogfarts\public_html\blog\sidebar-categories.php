<?php
/**
 * Blog Categories Sidebar Widget
 * 
 * This file displays a list of blog categories for the sidebar.
 */

// Get database connection if not already available
if (!isset($db) || !($db instanceof PDO)) {
    require_once '../config/db.php';
    $db = getDbConnection();
}

// Initialize variables
$categories = [];
$error = '';

try {
    // Fetch categories with post count
    $stmt = $db->prepare("
        SELECT 
            c.id, 
            c.name, 
            c.slug,
            COUNT(pc.post_id) as post_count
        FROM 
            categories c
        LEFT JOIN 
            post_categories pc ON c.id = pc.category_id
        LEFT JOIN 
            posts p ON pc.post_id = p.id AND p.status = 'published'
        GROUP BY 
            c.id
        HAVING 
            post_count > 0
        ORDER BY 
            c.name ASC
    ");
    
    $stmt->execute();
    $categories = $stmt->fetchAll();
} catch (PDOException $e) {
    // Log the error (in a production environment)
    error_log('Database query error: ' . $e->getMessage());
    
    // Set error message
    $error = 'Could not retrieve categories.';
}
?>

<div class="widget blog-categories">
    <h3>Categories</h3>
    
    <?php if ($error): ?>
        <p class="error"><?php echo htmlspecialchars($error); ?></p>
    <?php elseif (empty($categories)): ?>
        <p>No categories found.</p>
    <?php else: ?>
        <ul>
            <?php foreach ($categories as $category): ?>
                <li>
                    <a href="category.php?slug=<?php echo htmlspecialchars($category['slug']); ?>">
                        <?php echo htmlspecialchars($category['name']); ?>
                        <span class="count">(<?php echo $category['post_count']; ?>)</span>
                    </a>
                </li>
            <?php endforeach; ?>
        </ul>
    <?php endif; ?>
</div>
